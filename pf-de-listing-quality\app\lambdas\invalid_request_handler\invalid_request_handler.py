import json
import boto3
import os
import time
import logging
from datetime import datetime, timezone, timedelta

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Create boto3 clients for AWS services
sqs_client = boto3.client("sqs")
dynamodb_client = boto3.resource("dynamodb")
s3_client = boto3.client("s3")
ssm_client = boto3.client('ssm')

# Get environment variables
ENV = os.getenv("ENV")

# Constants for resource naming and configuration
S3_DIRECTORY = "invalid-requests"
SSM_S3_BUCKET_KEY = f"/listing-quality/{ENV}/s3/tracking"
SSM_DYNAMODB_TABLE_KEY = f"/listing-quality/{ENV}/dynamodb/table"
SSM_DYNAMODB_TTL_KEY = f"/listing-quality/{ENV}/dynamodb/ttl"
SSM_NOTIFICATION_QUEUE_KEY = f"/listing-quality/{ENV}/queue/notification"

# Function to fetch SSM parameters for dynamic configuration
def fetch_ssm_parameters():
    # List of parameter names to retrieve from Parameter Store
    parameter_names = [
        SSM_S3_BUCKET_KEY,
        SSM_DYNAMODB_TABLE_KEY,
        SSM_DYNAMODB_TTL_KEY,
        SSM_NOTIFICATION_QUEUE_KEY
    ]
   
    try:
        # Retrieve all parameters in a single API call
        response = ssm_client.get_parameters(
            Names=parameter_names, WithDecryption=True
        )
        parameters = {}
        for param in response['Parameters']:
            parameters[param['Name']] = param['Value']
       
        return parameters
    except Exception as e:
        logger.error(f"Error fetching SSM parameters: {str(e)}", exc_info=True)
        raise

# Initialize configuration from Parameter Store
params = fetch_ssm_parameters()

# Extract configuration values from parameters
NOTIFICATION_SUCESS_QUEUE_URL = params.get(SSM_NOTIFICATION_QUEUE_KEY)
S3_BUCKET_NAME = params.get(SSM_S3_BUCKET_KEY)
DYNAMODB_TABLE_NAME = params.get(SSM_DYNAMODB_TABLE_KEY)
DYNAMODB_RECORD_TTL = int(params.get(SSM_DYNAMODB_TTL_KEY)) if params.get(SSM_DYNAMODB_TTL_KEY) else 2

# Initialize DynamoDB table resource if table name is available
dynamodb_table = dynamodb_client.Table(DYNAMODB_TABLE_NAME) if DYNAMODB_TABLE_NAME else None

def lambda_handler(event, context):
    """
    Main Lambda function handler that processes failed messages from the Dead Letter Queue.
    For each message, it logs the failure to DynamoDB, stores the message in S3,
    and forwards it to a notification queue.
    """
    try:
        for record in event["Records"]:
            try:
                message_id = record["messageId"]

                # Parse the message body as JSON
                body = record["body"]
                body = json.loads(body)

                submission_id = body.get("submission_id")

                logger.info(
                    f"Message Id: {message_id}, Request Id: {submission_id}, Body: {body}"
                )

                # Process each failed request by tracking it and sending notifications
                if submission_id:
                    log_failure_to_dynamodb(submission_id, body)
                    write_json_to_s3(submission_id, body)
                send_message_to_notification_queue(body)

            except Exception as e:
                # Continue processing remaining records even if one fails
                logger.error(f"Exception processing record: {str(e)}", exc_info=True)
                continue

        return {
            "statusCode": 200,
            "body": json.dumps(
                {"message": "Processed all failed requests successfully"}
            ),
        }

    except Exception as e:
        logger.error(f"Error processing DLQ messages: {str(e)}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


# Function to log failed request in DynamoDB
def log_failure_to_dynamodb(submission_id, body):
    """
    Stores information about failed requests in DynamoDB for tracking and analysis.
    Each record includes the request ID, type, error message, original payload,
    status, timestamp, and TTL for automatic cleanup.
    """
    try:
        data = {
            "RequestID": submission_id,
            "api_type": body.get("type"),
            "output": json.dumps({"error_message": "Request Failed"}),
            "payload": json.dumps(body),
            "status": "INVALID_REQUEST",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "ttl": int(
                (
                    datetime.now(timezone.utc) + timedelta(hours=DYNAMODB_RECORD_TTL)
                ).timestamp()
            ),
        }
        dynamodb_table.put_item(Item=data)
        logger.info("Inserted failed Request Id: %s", submission_id)  
    except Exception as e:
        logger.error(
            f"Failed to log Request Id: {submission_id} to DynamoDB: {str(e)}",
            exc_info=True,
        )
        raise Exception(f"Failed to log Request Id: {submission_id} to DynamoDB: {str(e)}")


# Function to store failed request JSON in S3 for debugging and audit purposes
def write_json_to_s3(submission_id, json_data):
    """
    Archives the original failed request data to S3 for long-term storage
    and future reference or debugging.
    """
    json_str = json.dumps(json_data)

    # Construct S3 object key with directory prefix and submission ID
    s3_path = f"{S3_DIRECTORY}/{submission_id}.json"

    try:
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=s3_path,
            Body=json_str,
            ContentType="application/json",
        )
        logger.info(
            f"Successfully uploaded Request Id: {submission_id}.json to S3://{S3_BUCKET_NAME}/{s3_path}"
        )
    except Exception as e:
        logger.error(
            f"Error uploading file to S3: {e}, Request Id: {submission_id}, data: {json_str}"
        )
        raise Exception(
            f"Error uploading file to S3: {e}, Request Id: {submission_id}, data: {json_str}"
        )


# Function to send failure notification to downstream systems via SQS
def send_message_to_notification_queue(body):
    """
    Forwards the failed request information to a notification queue
    so other systems can be informed about the failure and potentially
    take remedial actions.
    """
    try:
        sqs_client.send_message(
            QueueUrl=NOTIFICATION_SUCESS_QUEUE_URL, MessageBody=json.dumps(body)
        )
        logger.info("Successfully added message in notification queue.")
    except Exception as e:
        logger.exception("Failed to add message in notification queue.")