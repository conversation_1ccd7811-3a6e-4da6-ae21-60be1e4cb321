from fastapi import APIRouter

# Import the image quality validation router from the v1 API
from api.img_process.v1.quality import quality_router as quality_v1_router

# Create the main API router
router = APIRouter()

# Include the quality validation endpoints with a specific URL prefix and tag for OpenAPI docs
router.include_router(
    quality_v1_router, prefix="/validate_quality", tags=["Validate Quality of Image"]
)

# Export only the router so it can be imported elsewhere cleanly
__all__ = ["router"]

