import * as cdk from 'aws-cdk-lib';
import {Construct} from 'constructs';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import {ListingQualityParameters} from './parameters';
import {ListingQualityLambdaResources} from './lambdas';
import {QualityApiGateway} from './api_gateway'

export interface ListingQualityLambdaStackProps extends cdk.StackProps {
  // Rename 'env' to 'envName' to avoid conflict with the base class 'env' property
  envName?: string;
  imageTag?: string;
}

export class ListingQualityLambdaStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: ListingQualityLambdaStackProps) {
    super(scope, id, props);

    // Determine environment - default to 'staging' if not specified
    const stage = props?.envName || process.env.DEPLOYMENT_ENV || 'staging';

    // Get the image tag from props if available
    const imageTag = props?.imageTag;

    const resourceNameSuffix = stage === 'production' ? 'production' : 'staging';
    
    // Define environment-specific configurations
    const envConfig = {
      staging: {
        accountId: '************',
        region: 'ap-southeast-1',
        tags: {
          'Environment': 'staging'
        }
      },
      production: {
        accountId: '************',
        region: 'ap-southeast-1',
        tags: {
          'Environment': 'production'
        }
      }
    };

    // Select the appropriate configuration
    const config = stage === 'production' ? envConfig.production : envConfig.staging;

    // Define common tags with environment-specific values merged in
    const commonTags = {
      'Tribe': 'core-platform',
      'Team': 'compliance',
      'Service': 'listing-quality',
      'ManagedBy': 'cdk',
      'Project': 'gen-ai',
      ...config.tags
    };

    // Apply tags to stack
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this).add(key, value);
    });

    // Initialize parameters
    new ListingQualityParameters(this, 'Parameters', {
      stage: stage,
      resourceNameSuffix: resourceNameSuffix,
      tags: commonTags,
      accountId: config.accountId,
      region: config.region
    });
    
    // Create Lambda resources, passing the imageTag
    const lambdaResources = new ListingQualityLambdaResources({
      stage: stage,
      resourceNameSuffix: resourceNameSuffix,
      commonTags: commonTags,
      scope: this,
      accountId: config.accountId,
      region: config.region,
      imageTag: imageTag // Pass the image tag to the Lambda resources
    });

    // Create EventBridge rules for periodic triggering of Lambda functions
    // 1. Rule for dispatcher Lambda
    const dispatcherRule = new events.Rule(this, 'DispatcherRule', {
      ruleName: `listing-quality-dispatcher-rule-${resourceNameSuffix}`,
      description: `Triggers dispatcher lambda every minute for ${stage}`,
      enabled: true,
      schedule: events.Schedule.rate(cdk.Duration.minutes(1)),
      targets: [
        new targets.LambdaFunction(lambdaResources.dispatcherLambda, {
          retryAttempts: 3
        })
      ]
    });
    
    // Add tags to the rule
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(dispatcherRule).add(key, value);
    });

    // 2. Rule for request invocation Lambda
    const invocationRule = new events.Rule(this, 'InvocationRule', {
      ruleName: `listing-quality-invocation-rule-${resourceNameSuffix}`,
      description: `Triggers request invocation lambda every minute for ${stage}`,
      enabled: false,
      schedule: events.Schedule.rate(cdk.Duration.minutes(1)),
      targets: [
        new targets.LambdaFunction(lambdaResources.requestInvocationLambda, {
          retryAttempts: 3
        })
      ]
    });
    
    // Add tags to the rule
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(invocationRule).add(key, value);
    });

    // Quality API Gateway infra
    new QualityApiGateway(this, 'QualityApiGateway', {
      qualityResponseLambda: lambdaResources.qualityResponseLambda,
      stage: stage
    });
  }
}