"""
Module for interacting with Google Gemini LLM models using the new Google Gen AI Python SDK.
"""

import json
import ast
import time
import io
import os

from PIL import Image
from google import genai
from google.genai import types
import snoop

from config.config import config
from core.enums.status import ResponseStatus
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_debug, log_frame_warning

# Initialize logger for this module
logger = get_logger(__name__)

# Global configuration and client initialization
config_obj = config.config

# Ensure config_obj is a dictionary
if not isinstance(config_obj, dict):
    log_frame_warning(logger, "config_obj is not a dictionary, using empty config")
    config_obj_safe = {}
else:
    config_obj_safe = config_obj

gemini_api_key = config_obj_safe.get("gemini_api_key") or os.getenv("GEMINI_API_KEY")

# Retry configuration
gemini_max_attempts = 3

# Initialize Gemini client using the new SDK
gemini_client = None
if gemini_api_key:
    try:
        # Set the API key as environment variable for the new SDK
        os.environ['GEMINI_API_KEY'] = gemini_api_key
        # Create client with explicit API key
        gemini_client = genai.Client(api_key=gemini_api_key)
        log_frame_info(logger, message="Gemini client initialized successfully with new SDK")
    except Exception as e:
        log_frame_error(logger, message=f"Failed to initialize Gemini client: {str(e)}")
        gemini_client = None
else:
    log_frame_error(logger, message="No Gemini API key found in configuration")


class GeminiLlm:
    """
    A class for interacting with Google Gemini LLM models using the new Google Gen AI Python SDK.
    """

    @classmethod
    # @snoop
    def converse_image_text(cls, image_bytes, prompt):
        """
        Image+Text inference matching Bedrock schema:
        Returns { status, result, llm_usage } where result is a parsed object when possible.
        """
        try:
            if not gemini_client:
                return {"status": ResponseStatus.FAIL.value, "message": "Gemini client not initialized"}

            start_time = time.time()
            inference_config = config_obj_safe.get("gemini_inference_config", {})
            model_id = "models/gemini-2.5-flash"
            # inference_config.get("gemini_model_name") or "models/gemini-1.5-flash"

            config_kwargs = {
                "max_output_tokens": int(inference_config.get("gemini_max_tokens") or 4096),
                "temperature": float(inference_config.get("gemini_temperature") or 0.0),
                "top_p": float(inference_config.get("gemini_top_p") or 0.95),
                "candidate_count": int(inference_config.get("gemini_candidate_count") or 1),
            }

            # Ensure temperature is within valid range [0.0, 2.0] for Gemini
            config_kwargs["temperature"] = max(0.0, min(2.0, config_kwargs["temperature"]))
            # Ensure top_p is within valid range [0.0, 1.0] for Gemini
            config_kwargs["top_p"] = max(0.0, min(1.0, config_kwargs["top_p"]))

            # LOG: Image+Text model call details
            log_frame_info(logger, f"🖼️ GEMINI IMAGE+TEXT CALL STARTED")
            log_frame_info(logger, f"📋 Model: {model_id}")
            log_frame_info(logger, f"🔧 Config: temp={config_kwargs['temperature']}, max_tokens={config_kwargs['max_output_tokens']}, top_p={config_kwargs['top_p']}")
            log_frame_info(logger, f"🖼️ Image size: {len(image_bytes)} bytes")

            # LOG: Prompt content (truncated)
            prompt_preview = str(prompt)[:200] + "..." if len(str(prompt)) > 200 else str(prompt)
            log_frame_info(logger, f"💬 Prompt: {prompt}")
            log_frame_debug(logger, f"💬 Prompt type: {type(prompt).__name__}")

            # Prepare parts: image inline + prompt text
            parts = []
            try:
                # Ensure image bytes are PIL-valid; if not, still try sending raw
                img = Image.open(io.BytesIO(image_bytes))
                buf = io.BytesIO()
                img.save(buf, format='PNG')
                png_bytes = buf.getvalue()
            except Exception:
                png_bytes = image_bytes

            parts.append(types.Part(inline_data=types.Blob(mime_type="image/png", data=png_bytes)))

            # Ensure prompt is properly formatted as text
            if isinstance(prompt, dict):
                # If prompt is a dictionary, convert to JSON string
                prompt_text = json.dumps(prompt, ensure_ascii=False)
                log_frame_debug(logger, f"🔄 Converted dict prompt to JSON string ({len(prompt_text)} chars)")
            elif isinstance(prompt, str):
                # If prompt is already a string, use as-is
                prompt_text = prompt
                log_frame_debug(logger, f"✅ Using string prompt as-is ({len(prompt_text)} chars)")
            else:
                # For any other type, convert to string
                prompt_text = str(prompt)
                log_frame_debug(logger, f"🔄 Converted {type(prompt).__name__} prompt to string ({len(prompt_text)} chars)")

            parts.append(types.Part(text=prompt_text))

            # Build comprehensive function calling schema for single-call analysis
            try:
                function_declaration = types.FunctionDeclaration(
                    name="analyze_image_quality_comprehensive",
                    description="Comprehensive analysis of real-estate listing photos including quality metrics, watermark detection, and competitor identification",
                    parameters={
                        "type": "object",
                        "properties": {
                            # Core image quality fields
                            "rotation": {"type": "boolean", "description": "Whether the image is rotated"},
                            "quality_of_picture": {"type": "integer", "minimum": 0, "maximum": 10, "description": "Overall picture quality score"},
                            "distortion": {"type": "integer", "minimum": 0, "maximum": 10, "description": "Level of image distortion"},
                            "is_it_boxed": {"type": "boolean", "description": "Whether the image has a border/box"},
                            "people_on_photo": {"type": "boolean", "description": "Whether people are visible in the image"},
                            "photo_classification": {"type": "string", "description": "Type of room/area shown in the image"},
                            "furnished": {"type": "boolean", "description": "Whether the space appears furnished"},
                            "room_size": {"type": "string", "enum": ["small", "medium", "large", "unidentified room size", "not available"], "description": "Size of the room/space"},
                            "is_it_a_render": {"type": "boolean", "description": "Whether the image is a 3D render"},
                            "room_under_construction": {"type": "boolean", "description": "Whether the space is under construction"},
                            "room_category": {"type": "string", "description": "Style category of the room"},
                            # Comprehensive watermark detection fields
                            "text": {"type": "boolean", "description": "Whether any text or watermark text is present in the image"},
                            "watermark": {"type": "boolean", "description": "Whether any watermark is present in the image"},
                            "watermark_text": {"type": "string", "description": "The exact text content of any watermark found in the image"},
                            "is_competitor_watermark": {"type": "boolean", "description": "Whether the detected watermark text matches any of these competitors: bayut, dubbizzle, aqar, aqarmap, elbayt, re/max, wasalt, skyloov"},
                            "competitor_name": {"type": "string", "description": "The specific competitor name if a competitor watermark is detected, empty string otherwise"}
                        },
                        "required": [
                            "rotation",
                            "quality_of_picture",
                            "distortion",
                            "is_it_boxed",
                            "people_on_photo",
                            "photo_classification",
                            "furnished",
                            "room_size",
                            "is_it_a_render",
                            "room_under_construction",
                            "room_category",
                            "text",
                            "watermark",
                            "watermark_text",
                            "is_competitor_watermark",
                            "competitor_name"
                        ]
                    }
                )
                tools = [types.Tool(function_declarations=[function_declaration])]
                tool_config = types.ToolConfig(function_calling_config=types.FunctionCallingConfig(mode='ANY'))

                # LOG: Function schema for image analysis
                log_frame_info(logger, f"🛠️ IMAGE ANALYSIS FUNCTION SCHEMA:")
                log_frame_info(logger, f"   📋 Function: {function_declaration}")
                # log_frame_info(logger, f"   📝 Description: {function_declaration.description}")
                # log_frame_info(logger, f"   🔑 Properties: {len(function_declaration.parameters['properties'])}")
                # log_frame_debug(logger, f"   📋 Required fields: {', '.join(function_declaration.parameters['required'])}")

            except Exception as e:
                log_frame_error(logger, f"❌ Function schema creation failed: {e}")
                tools = None
                tool_config = None

            # LOG: Sending request
            log_frame_info(logger, f"🚀 SENDING IMAGE+TEXT REQUEST:")
            log_frame_info(logger, f"   🎯 Model: {model_id}")
            log_frame_info(logger, f"   📦 Parts: {len(parts)} (image + text)")
            log_frame_info(logger, f"   🛠️ Tools: {'Yes' if tools else 'No'}")

            response = None
            for attempt in range(gemini_max_attempts):
                try:
                    log_frame_debug(logger, f"🔄 Attempt {attempt + 1}/{gemini_max_attempts}")
                    response = gemini_client.models.generate_content(
                        model=model_id,
                        contents=[types.Content(role="user", parts=parts)],
                        config=types.GenerateContentConfig(
                            tools=tools,
                            tool_config=tool_config,
                            automatic_function_calling=types.AutomaticFunctionCallingConfig(disable=False),
                            **config_kwargs
                        )
                    )
                    log_frame_info(logger, f"✅ Image+Text request successful on attempt {attempt + 1}")
                    break
                except Exception as e:
                    if attempt == gemini_max_attempts - 1:
                        log_frame_error(logger, f"❌ All {gemini_max_attempts} attempts failed. Last error: {e}")
                        raise e
                    log_frame_warning(logger, f"⚠️ Retry attempt {attempt + 1} failed: {e}")
                    time.sleep(2 ** attempt)

            if response is None:
                return {"status": ResponseStatus.FAIL.value, "message": "No response from Gemini"}

            # LOG: Raw response analysis for image+text
            log_frame_info(logger, f"📥 IMAGE+TEXT RAW RESPONSE:")
            log_frame_info(logger, f"   📊 Response type: {type(response).__name__}")
            # Check for function calls first to avoid text access issues
            has_function_calls = hasattr(response, 'function_calls') and bool(response.function_calls)
            log_frame_info(logger, f"   🛠️ Has function_calls: {has_function_calls}")

            # Only check for text if no function calls (to avoid property access errors)
            has_text = False
            if not has_function_calls:
                try:
                    has_text = hasattr(response, 'text') and bool(response.text)
                except Exception:
                    has_text = False
            log_frame_info(logger, f"   🔍 Has text: {has_text}")

            # Prefer function-call arguments if available
            parsed = cls._extract_function_call_arguments(response)
            if parsed is not None:
                log_frame_info(logger, f"✅ Function call extracted successfully")
                log_frame_debug(logger, f"🔍 Parsed result: {parsed}")
            else:
                # Only try to access text if no function call was found and no function calls present
                log_frame_info(logger, f"⚠️ No function call found, trying text response")
                if not has_function_calls:
                    try:
                        raw = response.text if hasattr(response, 'text') else ""
                        log_frame_debug(logger, f"📄 Raw text: {raw[:200]}..." if len(str(raw)) > 200 else f"📄 Raw text: {raw}")
                        if isinstance(raw, str) and raw.strip():
                            try:
                                parsed = ast.literal_eval(raw)
                                log_frame_info(logger, f"✅ Successfully parsed text as literal")
                            except Exception as e:
                                log_frame_debug(logger, f"⚠️ Could not parse as literal: {e}")
                                parsed = raw
                        else:
                            parsed = raw
                    except Exception as e:
                        log_frame_error(logger, f"❌ Error accessing response text: {e}")
                        parsed = ""
                else:
                    log_frame_error(logger, f"❌ Function calls present but extraction failed")
                    parsed = ""

            usage_metadata = getattr(response, 'usage_metadata', None)
            if usage_metadata:
                llm_usage = {
                    # Bedrock-compatible format
                    "inputTokens": getattr(usage_metadata, 'prompt_token_count', 0),
                    "outputTokens": getattr(usage_metadata, 'candidates_token_count', 0),
                    "totalTokens": getattr(usage_metadata, 'total_token_count', 0),
                    # Additional Gemini-specific fields for compatibility
                    "promptTokenCount": getattr(usage_metadata, 'prompt_token_count', 0),
                    "candidatesTokenCount": getattr(usage_metadata, 'candidates_token_count', 0),
                    "totalTokenCount": getattr(usage_metadata, 'total_token_count', 0),
                    "latencyMs": int((time.time() - start_time) * 1000),
                }
            else:
                llm_usage = {
                    "inputTokens": 0,
                    "outputTokens": 0,
                    "totalTokens": 0,
                    "promptTokenCount": 0,
                    "candidatesTokenCount": 0,
                    "totalTokenCount": 0,
                    "latencyMs": int((time.time() - start_time) * 1000),
                }

            # LOG: Final image+text output and usage
            log_frame_info(logger, f"📊 IMAGE+TEXT FINAL OUTPUT:")
            log_frame_info(logger, f"   📋 Result type: {type(parsed).__name__}")
            log_frame_info(logger, f"   📏 Result size: {len(str(parsed))} characters")
            log_frame_info(logger, f"   🔢 Input tokens: {llm_usage['inputTokens']}")
            log_frame_info(logger, f"   🔢 Output tokens: {llm_usage['outputTokens']}")
            log_frame_info(logger, f"   🔢 Total tokens: {llm_usage['totalTokens']}")
            log_frame_info(logger, f"   ⏱️ Latency: {llm_usage['latencyMs']}ms")
            log_frame_info(logger, f"🎉 GEMINI IMAGE+TEXT CALL COMPLETED SUCCESSFULLY")

            return {"status": ResponseStatus.SUCCESS.value, "result": parsed, "llm_usage": llm_usage}

        except Exception as e:
            error_message = str(e)
            if "RATE_LIMIT_EXCEEDED" in error_message or "quota" in error_message.lower():
                log_frame_error(logger, message="Gemini model throttled:", error=error_message)
                return {"status": ResponseStatus.THROTTLE.value, "message": "Rate limit exceeded"}
            log_frame_error(logger, message="Error invoking Gemini image+text:", error=error_message)
            return {"status": ResponseStatus.FAIL.value, "message": f"ERROR: {error_message}"}

    @classmethod
    def _extract_function_call_arguments(cls, response):
        """
        Extract function call arguments from the new SDK response format with robust error handling.
        """
        try:
            log_frame_debug(logger, f"🔍 EXTRACTING FUNCTION CALL ARGUMENTS:")
            log_frame_debug(logger, f"   📊 Response type: {type(response).__name__}")

            # Check if response has function_calls attribute (new SDK format)
            if hasattr(response, 'function_calls') and response.function_calls:
                log_frame_debug(logger, f"   ✅ Found function_calls attribute with {len(response.function_calls)} calls")
                function_call = response.function_calls[0]  # Get first function call
                log_frame_debug(logger, f"   📋 Function name: {getattr(function_call, 'name', 'unknown')}")
                if hasattr(function_call, 'args'):
                    # Convert args to a standard Python dict with validation
                    args_dict = dict(function_call.args)
                    log_frame_debug(logger, f"   🔑 Raw args keys: {list(args_dict.keys())}")
                    log_frame_debug(logger, f"   📄 Raw args preview: {str(args_dict)[:200]}...")
                    return cls._validate_and_sanitize_function_args(args_dict)

            # Fallback: check candidates for function calls (legacy format)
            log_frame_debug(logger, f"   ⚠️ No direct function_calls, checking candidates...")
            candidates = getattr(response, 'candidates', [])
            log_frame_debug(logger, f"   📋 Found {len(candidates)} candidates")

            for i, candidate in enumerate(candidates):
                log_frame_debug(logger, f"   🔍 Checking candidate {i+1}")
                content = getattr(candidate, 'content', None)
                if content:
                    parts = getattr(content, 'parts', [])
                    log_frame_debug(logger, f"     📦 Found {len(parts)} parts")
                    for j, part in enumerate(parts):
                        if hasattr(part, 'function_call') and part.function_call:
                            log_frame_debug(logger, f"     ✅ Found function_call in part {j+1}")
                            if hasattr(part.function_call, 'args'):
                                args_dict = dict(part.function_call.args)
                                log_frame_debug(logger, f"     🔑 Legacy args keys: {list(args_dict.keys())}")
                                return cls._validate_and_sanitize_function_args(args_dict)

            log_frame_debug(logger, f"   ❌ No function calls found in response")
        except Exception as e:
            log_frame_error(logger, f"❌ Error extracting function call arguments: {e}")
        return None

    @classmethod
    def _validate_and_sanitize_function_args(cls, args_dict):
        """
        Validate and sanitize function call arguments to prevent JSON parsing errors.

        Args:
            args_dict: Dictionary of function call arguments

        Returns:
            Sanitized dictionary or None if validation fails
        """
        try:
            log_frame_debug(logger, f"🧹 VALIDATING AND SANITIZING FUNCTION ARGS:")
            log_frame_debug(logger, f"   📊 Input type: {type(args_dict).__name__}")

            if not isinstance(args_dict, dict):
                log_frame_error(logger, f"❌ Function args is not a dictionary: {type(args_dict)}")
                return None

            log_frame_debug(logger, f"   🔑 Input keys: {list(args_dict.keys())}")
            log_frame_debug(logger, f"   📏 Input size: {len(args_dict)} items")

            sanitized = {}
            for key, value in args_dict.items():
                # Sanitize string values that might contain problematic characters
                if isinstance(value, str):
                    # Remove or escape problematic characters that could break JSON
                    sanitized_value = value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    # Remove excessive whitespace
                    sanitized_value = ' '.join(sanitized_value.split())
                    # Escape quotes that might break JSON structure
                    sanitized_value = sanitized_value.replace('"', '\\"')
                    sanitized[key] = sanitized_value
                elif isinstance(value, (int, float, bool)) or value is None:
                    sanitized[key] = value
                elif isinstance(value, dict):
                    # Recursively sanitize nested dictionaries
                    nested_sanitized = cls._validate_and_sanitize_function_args(value)
                    if nested_sanitized is not None:
                        sanitized[key] = nested_sanitized
                    else:
                        log_frame_error(logger, f"Failed to sanitize nested dict for key: {key}")
                        sanitized[key] = {}
                elif isinstance(value, list):
                    # Sanitize list items
                    sanitized_list = []
                    for item in value:
                        if isinstance(item, str):
                            sanitized_item = item.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                            sanitized_item = ' '.join(sanitized_item.split())
                            sanitized_item = sanitized_item.replace('"', '\\"')
                            sanitized_list.append(sanitized_item)
                        else:
                            sanitized_list.append(item)
                    sanitized[key] = sanitized_list
                else:
                    # Convert other types to string and sanitize
                    str_value = str(value)
                    sanitized_value = str_value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    sanitized_value = ' '.join(sanitized_value.split())
                    sanitized_value = sanitized_value.replace('"', '\\"')
                    sanitized[key] = sanitized_value
                    log_frame_debug(logger, f"Converted {type(value)} to sanitized string for key: {key}")

            # Validate that the sanitized dict can be JSON serialized
            try:
                json.dumps(sanitized, ensure_ascii=False)
                log_frame_debug(logger, f"✅ Function args validation successful")
                log_frame_debug(logger, f"   🔑 Output keys: {list(sanitized.keys())}")
                log_frame_debug(logger, f"   📏 Output size: {len(sanitized)} items")
                return sanitized
            except (TypeError, ValueError) as e:
                log_frame_error(logger, f"❌ Sanitized args still not JSON serializable: {e}")
                return None

        except Exception as e:
            log_frame_error(logger, f"❌ Error validating function args: {e}")
            return None

    @classmethod
    def _convert_messages_to_gemini_format(cls, messages):
        """
        Convert messages to the new SDK format.
        """
        log_frame_debug(logger, f"🔄 CONVERTING MESSAGES TO GEMINI FORMAT:")
        log_frame_debug(logger, f"   📥 Input messages: {len(messages)}")

        contents = []
        for i, message in enumerate(messages):
            role = message.get("role")
            parts = []
            log_frame_debug(logger, f"   📋 Message {i+1}: role={role}")

            for j, content_item in enumerate(message.get("content", [])):
                if "text" in content_item:
                    # Add text part using the correct method
                    text_content = content_item["text"]
                    parts.append(types.Part(text=text_content))
                    log_frame_debug(logger, f"     📄 Part {j+1}: text ({len(text_content)} chars)")
                elif "image" in content_item:
                    # Add image part
                    image_data = content_item["image"]
                    if "source" in image_data and "bytes" in image_data["source"]:
                        try:
                            img = Image.open(io.BytesIO(image_data["source"]["bytes"]))
                            # Convert PIL image to base64 for the new SDK
                            img_buffer = io.BytesIO()
                            img.save(img_buffer, format='PNG')
                            img_bytes = img_buffer.getvalue()

                            # Create image part with proper data format
                            image_part = types.Part(
                                inline_data=types.Blob(
                                    mime_type="image/png",
                                    data=img_bytes
                                )
                            )
                            parts.append(image_part)
                            log_frame_debug(logger, f"     🖼️ Part {j+1}: image ({len(img_bytes)} bytes)")
                        except Exception as e:
                            log_frame_error(logger, f"❌ Could not process image bytes: {e}")

            if role in ["user", "model"] and parts:
                contents.append(types.Content(role=role, parts=parts))
                log_frame_debug(logger, f"   ✅ Added content for {role} with {len(parts)} parts")
            else:
                log_frame_debug(logger, f"   ⚠️ Skipped message {i+1}: invalid role or no parts")

        log_frame_debug(logger, f"   📤 Output contents: {len(contents)}")
        return contents