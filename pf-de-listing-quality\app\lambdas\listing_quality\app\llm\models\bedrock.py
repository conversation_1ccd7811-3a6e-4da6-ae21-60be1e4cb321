import ast
import json
import os
import re

import boto3
from botocore.config import Config
from config.config import config
from core.enums.status import ResponseStatus
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_warning,
    log_frame_debug,
    log_frame_error,
)
from core.utils.utils import get_json_value

# Load config object
config_obj = config.config

# Initialize logger for the current module
logger = get_logger(__name__)

# Load AWS test credentials from environment variables (used for test/local environments)
AWS_ACCESS_KEY_ID_TEST = os.getenv("AWS_ACCESS_KEY_ID_TEST", None)
AWS_SECRET_ACCESS_KEY_TEST = os.getenv("AWS_SECRET_ACCESS_KEY_TEST", None)
AWS_SESSION_TOKEN_TEST = os.getenv("AWS_SESSION_TOKEN_TEST", None)

# Determine Bedrock region from config or fallback to AWS default region
bedrock_region = config_obj.get("bedrock_model_region") or os.getenv(
    "AWS_DEFAULT_REGION"
)

# Get retry attempt count from config or use default
bedrock_max_attempts = int(
    get_json_value(config_obj.get("bedrock_inference_config"), "max_attempts") or 2
)

# Configure Bedrock with retry settings
bedrock_config = Config(
    retries={"total_max_attempts": bedrock_max_attempts, "mode": "standard"},
)

# Create a Boto3 session with test credentials if provided; otherwise, use default credentials
if AWS_ACCESS_KEY_ID_TEST and AWS_SECRET_ACCESS_KEY_TEST and AWS_SESSION_TOKEN_TEST:
    bedrock_session = boto3.Session(
        aws_access_key_id=AWS_ACCESS_KEY_ID_TEST,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY_TEST,
        aws_session_token=AWS_SESSION_TOKEN_TEST,
        region_name=bedrock_region,
    )
else:
    bedrock_session = boto3.Session(region_name=bedrock_region)

# Create a Bedrock runtime client from the session
bedrock_runtime = bedrock_session.client("bedrock-runtime", config=bedrock_config)


class BedrockLlm:
    @classmethod
    def invoke_model(cls, image_base64, prompt):
        # Log debug message before invoking the model
        log_frame_debug(
            logger, message="Invoking Bedrock model with prompt...", prompt=prompt[:50]
        )
        # Log first 50 chars
        log_frame_debug(
            logger, message="Image base64 size:", image_base64_size=len(image_base64)
        )

        # Log model config and region for debugging
        model_id = str(
            get_json_value(
                config_obj.get("bedrock_inference_config"), "bedrock_model_name"
            )
        )
        log_frame_info(
            logger,
            message="Bedrock invoke_model config",
            model_id=model_id,
            region=bedrock_region,
            bedrock_inference_config=config_obj.get("bedrock_inference_config"),
        )

        try:
            # Construct input payload for the model using config settings
            native_request = {
                "model_version": str(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"), "model_version"
                    )
                ),
                "max_tokens": int(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"), "bedrock_max_tokens"
                    )
                    or "1001"
                ),
                "temperature": int(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"),
                        "bedrock_temperature",
                    )
                    or "0"
                ),
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/jpeg",
                                    "data": f"{image_base64}",
                                },
                            },
                            {"type": "text", "text": f"{prompt}"},
                        ],
                    }
                ],
            }

            # Serialize payload to JSON
            request = json.dumps(native_request)

            log_frame_debug(
                logger, message="Model request payload:", request_payload=request[:500]
            )

            # Send request to Bedrock model
            response = bedrock_runtime.invoke_model(
                modelId=str(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"), "bedrock_model_name"
                    )
                ),
                contentType="application/json",
                accept="application/json",
                body=request,
            )

            # Read and parse model's response
            model_response = json.loads(response["body"].read())
            result = model_response["content"][0]["text"]

            log_frame_info(
                logger,
                message="Model invocation successful. Response preview",
                result=result[:100],
            )

            return result

        except bedrock_runtime.exceptions.ThrottlingException as e:
            # Handle throttling error with graceful response
            log_frame_warning(logger, message="Bedrock model throttled:", error=str(e))
            return {
                "status": ResponseStatus.THROTTLE.value,
                "message": f"ERROR:' {str(e)}",
                "data": None,
            }
        except Exception as e:
            # Handle any other errors with logging
            logger.exception("Error invoking Bedrock model: ", str(e))
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"ERROR:' {str(e)}",
                "data": None,
            }

    @classmethod
    def converse_model(cls, image, prompt, business_rules=None, is_verification=False):
        # Log info before making a conversational request
        log_frame_info(
            logger, message="Conversing Bedrock model with prompt:", prompt=prompt[:50]
        )

        # Log business rules and verification parameters (for compatibility with Gemini)
        log_frame_debug(logger, message=f"Business rules: {business_rules is not None}, Verification: {is_verification}")

        # Note: Bedrock doesn't support function calling like Gemini, so these parameters are logged but not used

        # Log model config and region for debugging
        model_id = str(
            get_json_value(
                config_obj.get("bedrock_inference_config"), "bedrock_model_name"
            )
        )
        log_frame_info(
            logger,
            message="Bedrock converse_model config",
            model_id=model_id,
            region=bedrock_region,
            bedrock_inference_config=config_obj.get("bedrock_inference_config"),
        )

        try:
            # Prepare model and inference settings
            model_id = str(
                get_json_value(
                    config_obj.get("bedrock_inference_config"), "bedrock_model_name"
                )
            )
            inferenceconfig = {
                "maxTokens": int(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"), "bedrock_max_tokens"
                    )
                    or "1000"
                ),
                "temperature": int(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"),
                        "bedrock_temperature",
                    )
                    or "0"
                ),
                "topP": int(
                    get_json_value(
                        config_obj.get("bedrock_inference_config"), "bedrock_top_p"
                    )
                    or "1"
                ),
            }

            # Prepare message structure for image + text conversation
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"image": {"format": "jpeg", "source": {"bytes": image}}},
                        {"text": prompt},
                    ],
                }
            ]
            request_payload = {
                "modelId": model_id,
                "inferenceConfig": inferenceconfig,
                "messages": messages,
            }

            log_frame_debug(
                logger,
                message="Model request payload:",
                request_payload=request_payload,
            )

            # Make a request to Bedrock's `converse` endpoint
            response = bedrock_runtime.converse(**request_payload)

            # Extract response content and usage info
            response_message = response["output"]["message"]
            result = response_message["content"][0]["text"]
            llm_usage = response["usage"] if "usage" in response else None

            # If metrics are available, merge them with usage info
            if llm_usage:
                llm_usage.update(response["metrics"])

            log_frame_info(
                logger,
                message="Model converse successful. Response preview:",
                result=result[:100],
            )
            log_frame_debug(logger, message=f"LLM usage {llm_usage}")

            return {
                "status": ResponseStatus.SUCCESS.value,
                "result": cls.parse_response(result),
                "llm_usage": llm_usage,
            }

        except bedrock_runtime.exceptions.ThrottlingException as e:
            # Handle throttling with meaningful message
            log_frame_error(logger, message="Bedrock model throttled:", error=str(e))
            return {
                "status": ResponseStatus.THROTTLE.value,
                "message": "Unable to process the request due to Throttling error",
                "data": None,
            }
        except Exception as e:  # pylint: disable=broad-except
            # Handle all other exceptions
            logger.exception("Error invoking Bedrock model: ", str(e))
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"ERROR: {str(e)}",
                "data": None,
            }

    @classmethod
    def parse_response(cls, response):
        # Check if the response is XML-like (e.g., <output>...</output>)
        if isinstance(response, str) and re.match(
            r"^\s*<[^>]+>.*<\/[^>]+>\s*$", response
        ):
            return response  # Return as string if no conversion works
        else:
            # If not XML, assume it's a raw Python literal (use ast.literal_eval carefully)
            try:
                return ast.literal_eval(response)
            except (ValueError, SyntaxError):
                return response  # Fallback to raw string if parsing fails
