import pytest
import boto3
import yaml
from moto import mock_aws

MOCK_BUCKET = "test-artifact-bucket"
MOCK_CONFIG_DIR = "test-config"
MOCK_CONFIG_KEY = f"{MOCK_CONFIG_DIR}/config.yaml"

MOCK_YAML_CONTENT = {
    "QUALITY_VALIDATION_CONFIG_PARAMETERS": {
        "score_threshold": 0.9,
        "expected_quality": "high"
    }
}

@pytest.fixture
def s3_with_yaml(monkeypatch):
    with mock_aws():  # Covers S3, DynamoDB, etc.
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=MOCK_BUCKET)

        yaml_data = yaml.dump(MOCK_YAML_CONTENT)
        s3.put_object(Bucket=MOCK_BUCKET, Key=MOCK_CONFIG_KEY, Body=yaml_data)

        # Monkeypatch config values used in PreConfigParameters
        from core.services import config_loader  # Update the path if needed

        monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts", MOCK_BUCKET)
        monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts_config", MOCK_CONFIG_DIR)

        yield  # This is used purely for setup

def test_retrieve_values_from_yaml_success(s3_with_yaml):
    from config.config_listing_params import PreConfigParameters
    config_instance = PreConfigParameters()

    result = config_instance.retrieve_values_from_yaml()

    assert result is not None
    assert "QUALITY_VALIDATION_CONFIG_PARAMETERS" in result
    assert result["QUALITY_VALIDATION_CONFIG_PARAMETERS"]["score_threshold"] == 0.9

def test_retrieve_values_from_yaml_failure(monkeypatch):
    from config.config_listing_params import PreConfigParameters
    config_instance = PreConfigParameters()
    # Simulate failure in get_object
    from core.services import config_loader

    monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts", "invalid-bucket")
    monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts_config", "invalid-dir")

    result = config_instance.retrieve_values_from_yaml()
    assert result is None

def test_get_quality_validation_secrets_success(s3_with_yaml):
    from config.config_listing_params import PreConfigParameters
    config_instance = PreConfigParameters()
    result = config_instance.get_quality_validation_secrets()
    assert result is not None
    assert result["score_threshold"] == 0.9


def test_get_quality_validation_secrets_missing_config(monkeypatch):
    from core.services import config_loader
    bad_yaml = yaml.dump({"OTHER_KEY": "value"})

    with mock_aws():
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket="mock-bucket")
        s3.put_object(Bucket="mock-bucket", Key="mock-dir/config.yaml", Body=bad_yaml)

        monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts", "mock-bucket")
        monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts_config", "mock-dir")

        from config.config_listing_params import PreConfigParameters
        config_instance = PreConfigParameters()

        result = config_instance.get_quality_validation_secrets()
        assert result is None


def test_get_quality_validation_secrets_failure(monkeypatch):
    from config.config_listing_params import PreConfigParameters
    config_instance = PreConfigParameters()
    class DummyPreConfig(PreConfigParameters):
        def retrieve_values_from_yaml(self):
            return None

    config_instance = DummyPreConfig()
    result = config_instance.get_quality_validation_secrets()
    assert result is None