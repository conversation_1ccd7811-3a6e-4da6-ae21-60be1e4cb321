import ast
import base64
import os
import re

from io import BytesIO

import cv2
import numpy as np
import requests
from PIL import Image
from botocore.exceptions import ClientError
import snoop



from app.img_process.quality.schema import PhotoAttributes
from app.llm.models.controller import ModelController
from config.config import config
from core.enums.status import ResponseStatus
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_error,
    log_frame_debug,
    log_frame_warning,
)
from core.services.config_loader import PreConfigParameters
from core.services.prompt_loads import PromptLoader
from .services.image_quality import get_boxed, image_quality
from .services.validation_attributes import ValidationAttributeDetails

# Retrieve the configuration object
config = config.config

# Fetch pre-configuration parameters related to quality validation secrets
pre_configs = PreConfigParameters.get_quality_validation_secrets()

# Get the environment variable "USE_BEDROCK_INVOKE" to check if Bedrock invoke is enabled
# Defaults to "false" if the environment variable is not set
use_bedrock_invoke = os.getenv("USE_BEDROCK_INVOKE", "false")

# Initialize the logger for the current module to capture logs
logger = get_logger(__name__)


class ImageQualityAnalysisModel:
    def __init__(self):
        # Initialize the prompts by loading them from the PromptLoader class
        self.prompts = PromptLoader._prompts

        # Create an instance of the ModelController to handle model-related operations
        self.model_controller = ModelController()

        # Create an instance of the ValidationAttributeDetails to handle validation details
        self.validation_attributes_details = ValidationAttributeDetails()

    def get_validation_details(
        self, photo_attributes: PhotoAttributes, processed_params: dict
    ) -> tuple:
        """Extracts validation details from processed parameters with error handling."""

        try:
            # Check if the photo_attributes is of the correct type (dict)
            if not isinstance(photo_attributes, dict):
                log_frame_warning(
                    logger, message="Invalid type for photo_attributes. Expected dict."
                )
                return [], []

            # Initialize lists to store validation details
            validation_details = []
            updated_validation_details = []

            # Exclude certain attributes from updated_validation_details
            for attr, val in photo_attributes.items():
                # Check if the attribute has a value and exists in processed_params
                if val and attr in processed_params:
                    detail = processed_params.get(attr)  # Safe lookup to avoid KeyError
                    validation_details.append(detail)

                    # Exclude certain attributes
                    if attr not in {"brightness", "watermark_text", "watermark"}:
                        updated_validation_details.append(detail)

            # Log the lengths of the extracted details
            log_frame_info(
                logger,
                message="Extracted validation details, updated details.",
                validation_details_len=len(validation_details),
                updated_validation_details_len=len(updated_validation_details),
            )

            # Return the lists of validation details
            return validation_details, updated_validation_details

        except Exception as e:
            # Log any errors that occur during the process
            log_frame_error(
                logger,
                message="Error extracting validation details",
                error=str(e),
                exc_info=True,
            )

            # Return empty lists in case of error
            return [], []

    def gray_image_brightness(self, im_file):
        """Calculates the brightness of a grayscale image with error handling."""

        try:
            # Validate the input: Ensure the image file exists and has 'content' attribute
            if not im_file or not hasattr(im_file, "content"):
                log_frame_warning(logger, "Invalid or missing image file.")
                return None

            # Convert the image content into a BytesIO stream and open it as a grayscale image
            image_content = BytesIO(im_file.content)
            try:
                # Open the image and convert it to grayscale ('L' mode)
                image = Image.open(image_content).convert("L")  # Convert to grayscale
            except Exception as e:
                # Log an error if the image fails to open or convert
                log_frame_error(
                    logger, message="Failed to open image", error=str(e), exc_info=True
                )
                return None

            # Convert the grayscale image to a NumPy array for further processing
            image_array = np.array(image)

            # Calculate the average intensity of the image (mean of pixel values)
            average_intensity = np.mean(image_array)  # Calculate mean intensity

            # Log the calculated average grayscale intensity for debugging purposes
            log_frame_debug(
                logger,
                message="Average grayscale intensity",
                average_intensity=average_intensity,
            )

            # Compute the brightness score based on the average intensity
            # The formula adjusts the brightness to a scale of 0 to 10
            brightness = np.clip(10 - abs(127 - average_intensity) / 12.7, 0, 10)
            brightness_score = round(brightness)

            # Log the calculated brightness score for informational purposes
            log_frame_info(
                logger,
                message="Calculated Brightness Score",
                brightness_score=brightness_score,
            )

            # Return the calculated brightness score
            return brightness_score

        except Exception as e:
            # Log any errors that occur during the process of calculating brightness
            log_frame_error(
                logger,
                message="Error in brightness calculation",
                error=str(e),
                exc_info=True,
            )
            return None  # Return None in case of error

    def get_photo_classification_prompt(self, key: str, value: list) -> str:
        """Generates a photo classification prompt from an S3 template with error handling."""

        try:
            # Validate input types: Ensure 'value' is a list
            if not isinstance(value, list):
                # If 'value' is not a list, log a warning and convert it to a list
                log_frame_warning(
                    logger,
                    "Expected list for 'value', got Converting to list.",
                    type(value),
                )

                # Convert to list, ensuring that it's empty if the value is None
                value = [str(value)] if value else []

            # Load the prompt template associated with the provided 'key' from S3
            prompt_template = PromptLoader.get_prompt(key)
            if not prompt_template:
                # If the prompt template is not found, log an error and return an empty string
                log_frame_error(
                    logger, message="Failed to load prompt template for key", key=key
                )
                return ""

            # Convert the list of values into a comma-separated string
            value_str = ", ".join(map(str, value))

            # Inject the 'key' and the 'value_str' into the prompt template to generate the classification prompt
            photo_classification_prompt = prompt_template.format(
                key=key, value=value_str
            )

            # Log the successful generation of the photo classification prompt
            log_frame_debug(
                logger,
                message="Photo classification prompt generated successfully for key.",
                key=key,
            )

            # Return the generated photo classification prompt
            return photo_classification_prompt

        except Exception as e:
            # If any error occurs during the process, log the error
            log_frame_error(
                logger,
                message="Error generating photo classification prompt.",
                error=str(e),
                exc_info=True,
            )
            return ""  # Return empty string in case of an error

    def get_room_category_prompt(self, key: str, value: list) -> str:
        """Generates a room category prompt from an S3 template with error handling."""

        try:
            # Validate input types: Ensure 'value' is a list
            if not isinstance(value, list):
                # If 'value' is not a list, log a warning and convert it to a list
                log_frame_warning(
                    logger, "Expected list for Converting to list.", type(value)
                )

                # Convert 'value' to a list if it's not already a list and if it is not empty
                value = [str(value)] if value else []

            # Load the prompt template associated with the provided 'key' from S3
            prompt_template = PromptLoader.get_prompt(key)
            if not prompt_template:
                # If the prompt template is not found, log an error and return an empty string
                log_frame_error(
                    logger, message="Failed to load prompt template for key", key=key
                )
                return ""

            # Convert the list of values into a comma-separated string
            value_str = ", ".join(map(str, value))

            # Inject the 'key' and 'value_str' into the prompt template to generate the room category prompt
            room_category_prompt = prompt_template.format(key=key, value=value_str)

            # Log the successful generation of the room category prompt
            log_frame_info(
                logger, message="Room category prompt generated successfully", key=key
            )

            # Return the generated room category prompt
            return room_category_prompt

        except Exception as e:
            # If any error occurs during the process, log the error
            log_frame_error(
                logger,
                message="Error generating room category prompt",
                error=str(e),
                exc_info=True,
            )

            # Return empty string in case of an error
            return ""
    # @snoop
    def get_comprehensive_prompt(self, image, attributes, processed_params, additional_data={}):
        """
        Generates a comprehensive single prompt that includes all image quality analysis and watermark detection.
        This replaces multiple separate prompts to enable single LLM call optimization.
        """
        try:
            # Load the picture quality prompt from the S3 template
            picture_quality_prompt = PromptLoader.get_prompt("picture")
            if not picture_quality_prompt:
                # Log an error if the prompt template is not loaded successfully
                log_frame_error(logger, "Failed to load picture quality prompt.")
                return "", ""

            # Ensure that image size is available in ValidationAttributeDetails
            if getattr(ValidationAttributeDetails, "image", None):
                # Extract width and height of the image and update the processed parameters
                width, height = ValidationAttributeDetails.image.size
                processed_params["quality_of_picture"] = picture_quality_prompt.format(
                    width=width, height=height
                )
                log_frame_debug(
                    logger,
                    message="Updated quality_of_picture",
                    processed_params=processed_params["quality_of_picture"],
                )
            else:
                # Log a warning if image is not available
                log_frame_warning(
                    logger,
                    "ValidationAttributeDetails.image is None. Skipping quality_of_picture update.",
                )

            # Load all necessary prompts in advance to minimize multiple S3 lookups
            prompt_keys = [
                "distortion",
                "rotation",
                "people_on_photo",
                "watermark",
                "room_under_construction",
                "is_it_a_render",
                "is_it_boxed",
                "furnished",
            ]

            # Create a dictionary to store all the loaded prompts for the keys
            prompts = {key: PromptLoader.get_prompt(key) for key in prompt_keys}

            # Update processed_params with the loaded prompts if they exist
            for key in prompt_keys:
                if key in processed_params and prompts.get(key):
                    processed_params[key] = prompts[key]
                    log_frame_debug(
                        logger,
                        message="Updated process params",
                        key=key,
                        value=processed_params[key],
                    )

            # Define a helper function to fetch preconfigured or custom values from additional_data
            def get_preconfigured_or_custom_value(key, default_value):
                """Returns preconfigured or custom values from additional_data."""
                return additional_data.get(key, default_value)

            # Process photo classification safely and update processed_params
            try:
                photo_classifications = get_preconfigured_or_custom_value(
                    "photo_classification",
                    ValidationAttributeDetails.pre_config_photo_classification,
                )
                processed_params["photo_classification"] = (
                    self.get_photo_classification_prompt(
                        "photo_classification", photo_classifications
                    )
                )
            except Exception as e:
                # Log error if photo classification prompt generation fails
                log_frame_error(
                    logger,
                    message="Failed to generate photo classification prompt.",
                    error=str(e),
                )
                processed_params["photo_classification"] = ""

            # Process room sizes and update processed_params
            try:
                room_sizes = get_preconfigured_or_custom_value(
                    "room_size", ValidationAttributeDetails.pre_config_room_size
                )
                processed_params["room_size"] = (
                    PromptLoader.get_prompt("room_size")
                    + f"[{', '.join(room_sizes)}]"
                    + PromptLoader.get_prompt("room_size_conditions")
                    + f"[{', '.join(room_sizes)}]"
                )
            except Exception as e:
                # Log error if room size prompt generation fails
                log_frame_error(
                    logger, message="Failed to generate room size prompts", error=str(e)
                )
                processed_params["room_size"] = ""

            # Process room categories and update processed_params
            try:
                room_categories = get_preconfigured_or_custom_value(
                    "room_category", ValidationAttributeDetails.pre_config_room_category
                )
                processed_params["room_category"] = self.get_room_category_prompt(
                    "room_category", room_categories
                )
            except Exception as e:
                # Log error if room category prompt generation fails
                log_frame_error(
                    logger,
                    message="Failed to generate room category prompt",
                    error=str(e),
                )
                processed_params["room_category"] = ""

            # Generate validation details and extract updated validation information
            processed_params["sample_output"] = ValidationAttributeDetails.sample_output
            _, updated_validation_details = self.get_validation_details(
                attributes, processed_params
            )
            updated_validation_text = "\n".join(updated_validation_details)

            # Remove unnecessary keys from sample_output to avoid overloading the prompt
            for key in ["brightness", "watermark", "watermark_text"]:
                processed_params["sample_output"].pop(key, None)

            # Safely generate the watermark prompt and log errors if any
            try:
                quality_metrics_prompt_template = PromptLoader.get_prompt(
                    "main_quality_metrics"
                )
                quality_metrics_prompt = quality_metrics_prompt_template.format(
                    updated_validation_details=updated_validation_text,
                    sample_output=processed_params["sample_output"],
                )
            except Exception as e:
                log_frame_error(
                    logger,
                    message="Failed to generate quality metrics prompt",
                    error=str(e),
                )
                quality_metrics_prompt = ""

            # Load configuration values from config.yaml with no hardcoded fallbacks
            # Get competitors from config.yaml
            config_competitors = []
            if pre_configs and "pre_config_competitors_list" in pre_configs:
                config_competitors = pre_configs["pre_config_competitors_list"]

            # Get photo classifications from event custom_input_list or config.yaml
            config_photo_classifications = []
            if additional_data.get("photo_classification"):
                config_photo_classifications = additional_data["photo_classification"]
            elif pre_configs and "pre_config_photo_classification" in pre_configs:
                config_photo_classifications = pre_configs["pre_config_photo_classification"]

            # Get room categories from event custom_input_list or config.yaml
            config_room_categories = []
            if additional_data.get("room_category"):
                config_room_categories = additional_data["room_category"]
            elif pre_configs and "pre_config_room_category" in pre_configs:
                config_room_categories = pre_configs["pre_config_room_category"]

            # Get room sizes from event custom_input_list or config.yaml
            config_room_sizes = []
            if additional_data.get("room_size"):
                config_room_sizes = additional_data["room_size"]
            elif pre_configs and "pre_config_room_size" in pre_configs:
                config_room_sizes = pre_configs["pre_config_room_size"]

            # Allow additional_data to override config values (for backward compatibility)
            competitor_list = additional_data.get("competitors", config_competitors)



            # Define default quality analysis rules using dynamic config values
            room_category_text = f"[{', '.join(config_room_categories)}]" if config_room_categories else "from provided list"
            room_size_text = f"[{', '.join(config_room_sizes)}]" if config_room_sizes else "appropriate size"

            default_quality_rules = f"""QUALITY ANALYSIS:
• Distortion (1-10): Evaluate geometric distortion - straight lines, perspective, scaling, ripple effects, stretching, lens distortion, texture consistency, object integrity
• Picture quality (1-10): Rate clarity for 856x550 resolution as viewed on 4K (3840x2160) screen - assess sharpness, detail, overall visual quality
• Rotation: Is image rotated? (boolean)
• People: Any people visible in photo? (boolean)
• Construction: Is room/property under construction? (boolean)
• Furnished: Is property furnished with furniture/fixtures? (boolean)
• Room category: Pick ONLY from {room_category_text} - return empty string if unclear or doesn't fit
• Rendered: Digital render vs real photo? Check shadows (natural vs artificial), reflections (accurate vs perfect), edges (clean vs natural), lighting (dramatic vs natural), imperfections (missing in renders)
• Boxed: Image altered into collage format? Distinguish from watermarks (boolean)
• Room size: Assess space {room_size_text} for complete rooms, "unidentified room size" if partial/unclear
• Photo classification: Pick ONE best match from provided categories - analyze all options, choose highest percentage match for major portion of image"""

            # Define default room size rules using dynamic config values
            room_size_details = ""
            if config_room_sizes:
                if "small" in [size.lower() for size in config_room_sizes]:
                    room_size_details += "- Small: Limited space, fixtures in close proximity, compact design\\n"
                if "medium" in [size.lower() for size in config_room_sizes]:
                    room_size_details += "- Medium: Standard fixtures with extra space, balanced layout\\n"
                if "large" in [size.lower() for size in config_room_sizes]:
                    room_size_details += "- Large: Ample space, multiple fixtures, spacious layout\\n"

            default_room_size_rules = f"""ROOM SIZE RULES:
{room_size_details}- Return "unidentified room size" when no room or only portion visible"""

            # Define default photo classification rules using dynamic config values
            photo_categories_text = f"Available categories: {', '.join(config_photo_classifications)}" if config_photo_classifications else "Use provided categories"

            default_photo_classification_rules = f"""PHOTO CLASSIFICATION RULES:
- If kitchen enclosed by glass doors = closed kitchen
- Multiple categories present = choose major portion
- Analyze ALL list values before deciding
- Don't pick first match - pick best percentage match
- {photo_categories_text}
- Return empty string if image doesn't fit any category"""

            # Define default watermark detection rules
            default_watermark_rules = f"""WATERMARK DETECTION:
• Detect watermarks/text: Any text or watermark present? (boolean)
• Extract text: If watermark detected, extract ALL visible text accurately
  - Include any language, letters, symbols, special characters, numbers
  - Multiple watermarks = extract all in order
  - Extract both English and Arabic text
  - Return only watermark text, not other image text
• Competitor analysis: Check if extracted text matches competitors: {', '.join(competitor_list)}
  - Consider exact matches and close variations
  - Account for spelling variations, case differences, partial matches
  - Example: "Bayut.com" matches "bayut"
  - Set is_competitor_watermark: true if match found, false otherwise
  - Set competitor_name: specific competitor if found, empty string otherwise"""

            # Get custom rules from additional_data or use defaults
            quality_rules = additional_data.get("quality_analysis_rules", default_quality_rules)
            room_size_rules = additional_data.get("room_size_rules", default_room_size_rules)
            photo_classification_rules = additional_data.get("photo_classification_rules", default_photo_classification_rules)
            watermark_rules = additional_data.get("watermark_detection_rules", default_watermark_rules)

            # Get additional custom instructions
            custom_instructions = additional_data.get("custom_instructions", "")

            # Generate enhanced comprehensive prompt for Gemini function calling
            try:
                comprehensive_prompt = f"""Analyze this real estate image comprehensively for quality metrics and watermark detection.

{quality_rules}

{room_size_rules}

{photo_classification_rules}

{watermark_rules}

Return structured data via function calling with all required fields.

{custom_instructions}"""

            except Exception as e:
                log_frame_error(
                    logger, message="Failed to generate optimized comprehensive prompt", error=str(e)
                )
                comprehensive_prompt = quality_metrics_prompt

            # Log the successful prompt generation
            log_frame_info(logger, "Successfully generated comprehensive single prompt.")
            # Return the comprehensive prompt (no separate watermark prompt needed)
            return comprehensive_prompt, ""

        except Exception as e:
            # Log any unexpected error during the prompt generation process
            log_frame_error(
                logger, message="Unexpected error in generating prompts", error=str(e)
            )
            # Return empty strings in case of an error
            return "", ""

    def get_new_prompt(self, image, attributes, processed_params, additional_data={}):
        """
        Legacy method for backward compatibility.
        Calls the new comprehensive prompt method and returns the first part.
        """
        comprehensive_prompt, _ = self.get_comprehensive_prompt(image, attributes, processed_params, additional_data)
        return comprehensive_prompt, ""

    def calculate_sharpness(self, response):
        """Calculates the sharpness of an image using Laplacian variance with error handling."""

        try:
            # Log start of sharpness calculation
            log_frame_info(logger, "Calculating sharpness for the provided image.")

            # Validate that the response is not None and contains image content
            if not response or not hasattr(response, "content"):
                log_frame_warning(logger, "Invalid or missing image response.")
                return None

            # Try to open the image and convert it to grayscale
            try:
                img = Image.open(BytesIO(response.content)).convert(
                    "L"
                )  # Convert directly to grayscale
            except Exception as e:
                # Log error if image opening or conversion fails
                log_frame_error(
                    logger, message="Failed to open or convert image", error=str(e)
                )
                return None

            # Convert the grayscale image to a NumPy array for processing
            img_array = np.array(img)
            if img_array.size == 0:
                # Log error if image array is empty (could be due to corrupt image)
                log_frame_error(logger, "Image conversion resulted in an empty array.")
                return None

            # Use OpenCV's Laplacian operator to compute image sharpness
            laplacian = cv2.Laplacian(img_array, cv2.CV_64F)
            sharpness_score = laplacian.var()

            # Log the computed sharpness score
            log_frame_info(
                logger,
                messsage="Calculated sharpness score",
                sharpness_score=sharpness_score,
            )

            # Return the sharpness score
            return sharpness_score

        except Exception as e:
            # Catch and log any unexpected errors
            log_frame_error(
                logger,
                message="Unexpected error in sharpness calculation",
                error=str(e),
            )
            return None

    def download_and_encode_image(self, image_url):
        """Downloads an image and encodes it in Base64. Converts PNG to JPEG if needed."""
        try:
            # Convert the input URL to a string to ensure it's in the correct format
            image_url = str(image_url)

            # Send a GET request to download the image
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                              "(KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
            }
            response = requests.get(
                image_url, headers=headers, timeout=10
            )

            # Raise an HTTPError if the response was unsuccessful (e.g., 404, 500)
            response.raise_for_status()

            image_bytes = response.content
            image_format = None
            # Check if the image is PNG and convert to JPEG if needed
            try:
                img = Image.open(BytesIO(image_bytes))
                image_format = img.format
                if img.format == 'PNG':
                    # Convert PNG to JPEG in memory
                    rgb_img = img.convert('RGB')
                    buffer = BytesIO()
                    rgb_img.save(buffer, format='JPEG')
                    buffer.seek(0)
                    image_bytes = buffer.read()
                    image_format = 'JPEG'
            except Exception as e:
                log_frame_warning(logger, f"Could not check/convert image format: {e}")
                # If conversion fails, fallback to original bytes

            # Check the configuration to determine if the image should be Base64 encoded
            if use_bedrock_invoke.lower() == "true":
                # Encode the image content to Base64 and decode it to a UTF-8 string
                image = base64.b64encode(image_bytes).decode("utf-8")
            else:
                # If not using base64, return raw image content
                image = image_bytes

            # Log successful download and conversion
            log_frame_info(logger, f"Image downloaded successfully. Format sent to Bedrock: {image_format}")

            # Return both the HTTP response and the image data (encoded or raw)
            return response, image
        except requests.exceptions.RequestException as e:
            # Catch and log any network-related exceptions (connection error, timeout, etc.)
            log_frame_error(
                logger, message="Image download failed", error=str(e), exc_info=True
            )

            # Return None for both response and image in case of failure
            return None, None

    def analyze_image_quality(self, response):
        """Analyzes sharpness, brightness, and other quality attributes."""

        # Calculate the sharpness of the image using Laplacian variance
        sharpness_variance = self.calculate_sharpness(response)
        log_frame_info(
            logger,
            message="Calculated sharpness variance",
            sharpness_variance=sharpness_variance,
        )

        # Analyze other image quality attributes (e.g., contrast, noise, exposure)
        image_quality_factors = image_quality(response)
        log_frame_info(logger, "Image quality factors calculated")

        # Measure the brightness score by converting the image to grayscale
        brightness_score = self.gray_image_brightness(response)
        log_frame_info(logger, "Brightness score calculated")

        # Return all quality metrics as a tuple
        return sharpness_variance, image_quality_factors, brightness_score

    def should_use_mock_response(self):
        """Returns True if MOCK_LLM_RESPONSE is set to true."""

        # Retrieve the environment variable value (defaults to "False" if not set)
        raw_mock_value = os.getenv("MOCK_LLM_RESPONSE", "False")

        # Log the evaluated result (True/False)
        log_frame_info(
            logger,
            message="Raw config value for mock_llm_response",
            raw_mock_value=raw_mock_value,
        )

        # Normalize the value: strip whitespace, convert to lowercase, and check if it equals "true"
        evaluated = str(raw_mock_value).strip().lower() == "true"
        log_frame_info(logger, message="Evaluated mock mode", evaluated=evaluated)

        # Return the final boolean result
        return evaluated

    def process_llm_response(self, image, prompt, mock_response):
        """Handles LLM call or returns mock response if enabled. Also checks for throttle errors."""

        # Check whether mock mode is enabled via environment config
        use_mock_llm_response = self.should_use_mock_response()

        # If mock mode is enabled, return a mocked LLM response
        if use_mock_llm_response:
            log_frame_info(logger, "Returning mock LLM response.")
            return {
                "status": ResponseStatus.SUCCESS.value,
                "result": ast.literal_eval(mock_response),
                "llm_usage": {},
            }

        # If not in mock mode, invoke the actual LLM model with the given image and prompt
        response = self.model_controller.get_model_instance(image, prompt)

        # Return the actual model response
        return response

    def process_watermark_detection(
        self, image, processed_params, watermark_prompt, watermark_sample
    ):
        """Handles watermark detection using LLM."""

        # Load the watermark detection prompt and inject the watermark sample into it
        watermark_prompt_text = PromptLoader.get_prompt("watermark_detect").format(
            new_watermark_sample=watermark_sample
        )

        # Combine the base watermark prompt with the formatted detection prompt
        combined_prompt = f"{watermark_prompt} {watermark_prompt_text}"

        # Call LLM or return mock response based on config
        mock_watermark_response = (
            "{'text': True, 'watermark_text': 'THE BEST', 'watermark': True}"
        )
        watermark_analysis_response = self.process_llm_response(
            image, combined_prompt, mock_watermark_response
        )

        # Check if the LLM call failed
        status = watermark_analysis_response.get("status")
        if status and status != ResponseStatus.SUCCESS.value:
            return watermark_analysis_response

        # Extract LLM usage stats
        llm_usage = {
            "watermark_detect_llm_usage": watermark_analysis_response.get("llm_usage"),
        }

        # Extract watermark analysis results from LLM response
        watermark_analysis_results = watermark_analysis_response.get("result")

        # If result is missing, return the original response
        if watermark_analysis_results is None:
            return watermark_analysis_response

        # Check if text was detected in the watermark (with fallback for missing field)
        text_detected = watermark_analysis_results.get("text", False)
        watermark_data = {
            "watermark": watermark_analysis_results.get("watermark", False),
            "watermark_text": watermark_analysis_results.get("watermark_text", "")
            if text_detected
            else "",
        }

        # No additional competitor validation - watermark detection is complete

        # Return the final result with watermark data and LLM usage details
        return {"result": watermark_data, "llm_usage": llm_usage}

    @snoop
    def process_images(self, image_url, attributes, processed_params, other_vals={}):
        """Main function to process image, analyze quality, detect watermark, and return results."""
        try:
            # Step 1: Download and encode the image (Base64 or raw bytes based on config)
            response, downloaded_image = self.download_and_encode_image(image_url)
            if response is None:
                # Return failure response if download fails
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "Image download failed",
                    "data": None,
                }

            # Step 2: Analyze image quality (sharpness, quality factors, brightness)
            _, image_quality_factors, brightness_score = self.analyze_image_quality(
                response
            )

            # Step 3: Set static attribute image for downstream usage
            ValidationAttributeDetails.image = Image.open(BytesIO(response.content))

            # Step 4: Detect if the image is boxed
            is_boxed = get_boxed(image_url)
            log_frame_debug(logger, message="Boxed parameter", is_boxed=is_boxed)

            # Step 5: Prepare comprehensive prompt for single LLM call (includes quality + watermark analysis)
            comprehensive_prompt, _ = self.get_comprehensive_prompt(
                image_url, attributes, processed_params, other_vals
            )

            # Step 6: Define comprehensive mock LLM response for testing or mock mode
            mock_comprehensive_response = "{'rotation': False, 'quality_of_picture': 8, 'distortion': 2, 'is_it_boxed': False, 'people_on_photo': False, 'photo_classification': 'dining room', 'furnished': True, 'room_size': 'medium', 'is_it_a_render': False, 'room_under_construction': False, 'room_category': 'Modern', 'text': True, 'watermark': True, 'watermark_text': 'THE BEST', 'is_competitor_watermark': False, 'competitor_name': ''}"  # noqa: E501
            comprehensive_analysis_response = self.process_llm_response(
                downloaded_image, comprehensive_prompt, mock_comprehensive_response
            )

            # Step 7: Check comprehensive analysis response status
            status = comprehensive_analysis_response.get("status")
            if status and status != ResponseStatus.SUCCESS.value:
                return comprehensive_analysis_response

            # Step 8: Extract LLM usage info and results from comprehensive analysis
            llm_usage = {
                "comprehensive_llm_usage": comprehensive_analysis_response.get("llm_usage"),
            }
            comprehensive_results = comprehensive_analysis_response.get("result")
            if comprehensive_results is None:
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "Comprehensive LLM processing failed",
                    "data": None,
                }

            # Step 9: Watermark processing is now included in comprehensive analysis
            # Extract watermark-related fields from comprehensive results
            if "watermark" in processed_params:
                # Watermark detection and competitor analysis already completed in single call
                # Check if competitor watermark was detected and handle validation
                is_competitor = comprehensive_results.get("is_competitor_watermark", False)
                competitor_name = comprehensive_results.get("competitor_name", "")

                if is_competitor and competitor_name:
                    log_frame_info(logger, f"Competitor watermark detected: {competitor_name}")
                    # Additional validation could be added here if needed
                    # But the LLM has already performed the competitor detection
                else:
                    log_frame_info(logger, "No competitor watermark detected")

                # No additional LLM usage since everything was done in single call

            # Step 12: Merge quality_of_picture score from LLM and calculated image quality
            if "quality_of_picture" in processed_params:
                llm_quality = comprehensive_results["quality_of_picture"]
                comprehensive_results["quality_of_picture"] = round(
                    0.3 * float(llm_quality)
                    + 0.7 * float(image_quality_factors["total_quality"])
                )
                comprehensive_results["quality_of_details"] = {
                    "factors_quality": image_quality_factors,
                    "llm_quality": llm_quality,
                }

            # Step 13: Add brightness score if requested
            if "brightness" in processed_params:
                comprehensive_results["brightness"] = brightness_score

            # Step 14: Validate photo classification against allowed list
            if "photo_classification" in processed_params:
                photo_classification_list = other_vals.get(
                    "photo_classification",
                    ValidationAttributeDetails.pre_config_photo_classification,
                )
                if (
                    comprehensive_results["photo_classification"]
                    not in photo_classification_list
                ):
                    comprehensive_results["photo_classification"] = "not available"

            # Step 15: Update boxed status in results
            comprehensive_results["is_it_boxed"] = "True" if is_boxed else "False"

            # Step 17: Return success with all comprehensive processed data
            return {
                "status": ResponseStatus.SUCCESS.value,
                "message": "Comprehensive processing completed successfully",
                "data": comprehensive_results,
                "llm_usage": llm_usage,
            }

        except Exception as e:
            # Catch any unexpected errors and return failure response
            log_frame_error(
                logger, message="Unexpected error in processing:", error=str(e)
            )
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Unexpected error occurred: {str(e)}",
                "data": None,
            }
