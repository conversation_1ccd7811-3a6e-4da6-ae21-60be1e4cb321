import json
import requests
import argparse
from typing import List, Dict, Any, Optional


def create_request_payload(
    listing_id: int,
    country_code: str,
    image_urls: List[str],
    validate_params: Optional[List[str]] = None,
    custom_input_list: Optional[Dict[str, List[str]]] = None
) -> Dict[str, Any]:
    """
    Create the request payload for the listing quality endpoint.
    
    Args:
        listing_id: The ID of the listing
        country_code: The country code (e.g., 'AE')
        image_urls: List of image URLs to validate
        validate_params: Optional list of validation parameters
        custom_input_list: Optional custom input lists for classification
        
    Returns:
        Dictionary containing the formatted request payload
    """
    # Default custom input list if not provided
    if custom_input_list is None:
        custom_input_list = {
            "photo_classification": [
                "Open kitchen", "Closed kitchen", "dining room", "bedroom", 
                "bathroom", "brochure", "floor plan", "map", "logo", 
                "living room", "garden", "balcony", "building exterior", 
                "parking", "MaidRoom", "laundry", "Corridor", "multiple_images"
            ],
            "room_category": ["Luxury", "Modern", "Classic"],
            "room_size": ["small", "medium", "large"]
        }
    
    # Create images list with IDs
    images = [{"id": i, "src": url} for i, url in enumerate(image_urls)]
    
    # Create the body payload
    body = {
        "listing_id": listing_id,
        "country_code": country_code,
        "validate_params": validate_params or [],
        "images": images,
        "custom_input_list": custom_input_list
    }
    
    # Create the full event payload
    event = {
        "resource": "/validate_quality",
        "path": "/validate_quality",
        "httpMethod": "POST",
        "requestContext": {},
        "multiValueQueryStringParameters": None,
        "body": json.dumps(body)
    }
    
    return event


def test_staging_endpoint(
    endpoint_url: str,
    listing_id: int,
    country_code: str,
    image_urls: List[str],
    validate_params: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Test the staging endpoint with the provided images.
    
    Args:
        endpoint_url: The URL of the staging endpoint
        listing_id: The ID of the listing
        country_code: The country code (e.g., 'AE')
        image_urls: List of image URLs to validate
        validate_params: Optional list of validation parameters
        
    Returns:
        The response from the endpoint
    """
    # Create the request payload
    payload = create_request_payload(
        listing_id=listing_id,
        country_code=country_code,
        image_urls=image_urls,
        validate_params=validate_params
    )
    
    # For direct API call, we need to extract the body content
    body_content = json.loads(payload["body"])
    
    # Make the request to the endpoint
    response = requests.post(endpoint_url, json=body_content)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return {"error": response.status_code, "message": response.text}


def main():
    parser = argparse.ArgumentParser(description="Test the listing quality staging endpoint with multiple images")
    parser.add_argument("--endpoint", required=True, help="The staging endpoint URL")
    parser.add_argument("--listing-id", type=int, default=43345345, help="Listing ID")
    parser.add_argument("--country-code", default="AE", help="Country code")
    parser.add_argument("--images", required=True, nargs="+", help="List of image URLs to test")
    parser.add_argument("--validate-params", nargs="*", default=[], help="Optional validation parameters")
    
    args = parser.parse_args()
    
    print(f"Testing endpoint: {args.endpoint}")
    print(f"With {len(args.images)} images")
    
    response = test_staging_endpoint(
        endpoint_url=args.endpoint,
        listing_id=args.listing_id,
        country_code=args.country_code,
        image_urls=args.images,
        validate_params=args.validate_params
    )
    
    print("\nResponse:")
    print(json.dumps(response, indent=2))


if __name__ == "__main__":
    main()
