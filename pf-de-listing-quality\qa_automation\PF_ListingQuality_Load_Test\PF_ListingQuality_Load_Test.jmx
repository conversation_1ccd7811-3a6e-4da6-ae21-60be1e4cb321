<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="TP_PF_Listing_Quality_Test">
      <boolProp name="TestPlan.functional_mode">true</boolProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">true</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="TG_Standard_User_Simulation">
        <intProp name="ThreadGroup.num_threads">1000</intProp>
        <intProp name="ThreadGroup.ramp_time">3000</intProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <CSVDataSet guiclass="TestBeanGUI" testclass="CSVDataSet" testname="CSV_Listing_Data">
          <stringProp name="filename">E:/My D/apache-jmeter-5.6.3/apache-jmeter-5.6.3/bin/input_data_pf.csv</stringProp>
          <stringProp name="fileEncoding"></stringProp>
          <stringProp name="variableNames"></stringProp>
          <boolProp name="ignoreFirstLine">true</boolProp>
          <stringProp name="delimiter">,</stringProp>
          <boolProp name="quotedData">true</boolProp>
          <boolProp name="recycle">true</boolProp>
          <boolProp name="stopThread">false</boolProp>
          <stringProp name="shareMode">shareMode.all</stringProp>
        </CSVDataSet>
        <hashTree/>
        <JSR223Sampler guiclass="TestBeanGUI" testclass="JSR223Sampler" testname="Listing_Quality_Load_Testing_Sampler">
          <stringProp name="scriptLanguage">groovy</stringProp>
          <stringProp name="parameters"></stringProp>
          <stringProp name="filename"></stringProp>
          <stringProp name="cacheKey">true</stringProp>
          <stringProp name="script">
import com.amazonaws.auth.profile.ProfileCredentialsProvider
import com.amazonaws.services.sqs.AmazonSQSClientBuilder
import com.amazonaws.services.sqs.model.SendMessageRequest
import com.amazonaws.services.sqs.model.AmazonSQSException
import groovy.json.JsonOutput
import java.util.UUID

// === AWS Config ===
def region  = &quot;ap-southeast-1&quot;
def profile = &quot;************_northbay-staging&quot;

// === GenAI Queue URL ===
def genAiQueueUrl = &quot;https://sqs.ap-southeast-1.amazonaws.com/************/listing-quality-gen-ai-request-queue-staging&quot;

// === Read Variables from JMeter ===
def listingId     = vars.get(&quot;listing_id&quot;)
def imagesCSV     = vars.get(&quot;images&quot;)
def customerType  = vars.get(&quot;customer_type&quot;)?.toLowerCase()?.trim() ?: &quot;ruby&quot;

// === Input Validation ===
if (!listingId) {
    SampleResult.setSuccessful(false)
    SampleResult.setResponseData(&quot;Missing listing_id&quot;, &quot;UTF-8&quot;)
    log.error(&quot;Missing listing_id&quot;)
    return
}

// === Generate Unique submission_id with prefix ===
def submissionId = &quot;am_&quot; + UUID.randomUUID().toString()
vars.put(&quot;submission_id&quot;, submissionId)

// === Build Image List ===
def imagesArray = imagesCSV.split(&quot;,&quot;)
def imagesList = []
for (int i = 0; i &lt; imagesArray.length; i++) {
    imagesList &lt;&lt; [ id: i, src: imagesArray[i].trim() ]
}

// === Construct Payload ===
def payload = [
    submission_id     : submissionId,
    customer_type     : customerType,
    type              : &quot;IMAGE_VALIDATION&quot;,
    listing_id        : listingId.toLong(),
    country_code      : &quot;EG&quot;,
    validate_params   : [],
    images            : imagesList,
    custom_input_list : [:]
]

// === Initialize SQS Client ===
def sqs = AmazonSQSClientBuilder.standard()
    .withRegion(region)
    .withCredentials(new ProfileCredentialsProvider(profile))
    .build()

// === Send Message to GenAI Queue ===
String responseSummary = &quot;&quot;
try {
    def messageJson = JsonOutput.prettyPrint(JsonOutput.toJson(payload))

    // Log request payload in report
    SampleResult.setSamplerData(&quot;SQS SendMessage to ${genAiQueueUrl}\n\nPayload:\n${messageJson}&quot;)

    def sendRequest = new SendMessageRequest()
        .withQueueUrl(genAiQueueUrl)
        .withMessageBody(messageJson)
    sqs.sendMessage(sendRequest)

    responseSummary = &quot;Sent to GenAI Queue\nSubmissionID: ${submissionId}&quot;
    SampleResult.setSuccessful(true)
    log.info(responseSummary)

} catch (AmazonSQSException e) {
    responseSummary = &quot;SQS Error: ${e.getMessage()}\nSubmissionID: ${submissionId}&quot;
    SampleResult.setSuccessful(false)
    log.error(&quot;SQS Error: &quot; + e.toString())
} catch (Exception e) {
    responseSummary = &quot;Unexpected Error: ${e.getMessage()}\nSubmissionID: ${submissionId}&quot;
    SampleResult.setSuccessful(false)
    log.error(&quot;Exception: &quot; + e.toString())
}

// === Set Response in JMeter for reporting ===
SampleResult.setResponseData(responseSummary, &quot;UTF-8&quot;)

// === Wait: Read from JMeter variable (50 minutes for 1000 requests) ===
// === Wait: Read from JMeter variable (30 minutes for 500 requests) ===
// === Wait: Read from JMeter variable (minutes for 300 requests) ===
def sleepMinutes = vars.get(&quot;sleep_minutes&quot;)?.toInteger() ?: 50
def sleepMillis = sleepMinutes * 60 * 1000

log.info(&quot;Sleeping for ${sleepMinutes} minutes (${sleepMillis} ms) before next action...&quot;)
Thread.sleep(sleepMillis)
log.info(&quot;Finished waiting. Proceeding to next step.&quot;)</stringProp>
        </JSR223Sampler>
        <hashTree/>
        <JSR223Sampler guiclass="TestBeanGUI" testclass="JSR223Sampler" testname="DynamoDB_Read_Sampler">
          <stringProp name="scriptLanguage">groovy</stringProp>
          <stringProp name="parameters"></stringProp>
          <stringProp name="filename"></stringProp>
          <stringProp name="cacheKey">true</stringProp>
          <stringProp name="script">import com.amazonaws.auth.profile.ProfileCredentialsProvider
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder
import com.amazonaws.services.dynamodbv2.model.GetItemRequest
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import groovy.json.JsonOutput
import groovy.json.JsonSlurper

// Config
String region = &quot;ap-southeast-1&quot;
String tableName = &quot;listing-quality-requests-tracking-staging&quot;
int initialDelayMinutes = 20
int maxRetries = 2
int retryDelaySeconds = 60

// Submission ID from SQS script
String submissionId = vars.get(&quot;submission_id&quot;)

if (!submissionId) {
    log.error(&quot;No SubmissionID found in variables&quot;)
    throw new Exception(&quot;SubmissionID not found in JMeter vars&quot;)
}

log.info(&quot;Checking DynamoDB for RequestID = ${submissionId}&quot;)

// Delay before first read
log.info(&quot;Waiting ${initialDelayMinutes} minutes before first check...&quot;)
Thread.sleep(initialDelayMinutes * 60 * 1000)

// DynamoDB Client
AmazonDynamoDB dynamoDB = AmazonDynamoDBClientBuilder.standard()
    .withRegion(region)
//    .withCredentials(new DefaultAWSCredentialsProviderChain())
    .withCredentials(new ProfileCredentialsProvider(&quot;************_northbay-staging&quot;))
    .build()

// Retry Logic
def item = null
def lastError = null

for (int attempt = 1; attempt &lt;= maxRetries; attempt++) {
    try {
        log.info(&quot;Attempt ${attempt}/${maxRetries} - Fetching from DynamoDB...&quot;)

        GetItemRequest request = new GetItemRequest()
            .withTableName(tableName)
            .withKey([&quot;RequestID&quot;: new AttributeValue().withS(submissionId)])

        def result = dynamoDB.getItem(request)
        item = result.getItem()

        if (item) {
            log.info(&quot;Record found.&quot;)
            break
        } else {
            lastError = &quot;Record not found&quot;
            log.info(&quot;${lastError}. Waiting ${retryDelaySeconds} seconds before retry...&quot;)
            Thread.sleep(retryDelaySeconds * 1000)
        }
    } catch (Exception e) {
        lastError = &quot;DynamoDB query failed: ${e.message}&quot;
        log.error(&quot;${lastError}&quot;)
        Thread.sleep(retryDelaySeconds * 1000)
    }
}

if (!item) {
    throw new Exception(&quot;Failed after ${maxRetries} attempts. Last error: ${lastError}&quot;)
}

// Extract and parse
def extractedData = [
    submission_id: submissionId,
    status: item.get(&quot;status&quot;)?.getS() ?: &quot;NOT_FOUND&quot;,
    output: item.get(&quot;output&quot;)?.getS() ?: &quot;{}&quot;,
    payload: item.get(&quot;payload&quot;)?.getS() ?: &quot;{}&quot;,
    timestamp: item.get(&quot;timestamp&quot;)?.getS() ?: &quot;NOT_AVAILABLE&quot;
]

try {
    if (extractedData.output != &quot;{}&quot;) {
        extractedData.parsed_output = new JsonSlurper().parseText(extractedData.output)
    }
    if (extractedData.payload != &quot;{}&quot;) {
        extractedData.parsed_payload = new JsonSlurper().parseText(extractedData.payload)
    }
} catch (Exception e) {
    log.warn(&quot;Failed to parse JSON fields: ${e.message}&quot;)
}

// Store in JMeter vars
vars.put(&quot;dynamodb_submission_id&quot;, extractedData.submission_id)
vars.put(&quot;dynamodb_status&quot;, extractedData.status)
vars.put(&quot;dynamodb_output&quot;, extractedData.output)
vars.put(&quot;dynamodb_payload&quot;, extractedData.payload)
vars.put(&quot;dynamodb_timestamp&quot;, extractedData.timestamp)

// Log
log.info(&quot;DynamoDB Record:&quot;)
log.info(&quot;   Status: ${extractedData.status}&quot;)
log.info(&quot;   Timestamp: ${extractedData.timestamp}&quot;)
log.info(&quot;   Output: ${extractedData.output.take(100)}...&quot;)
log.info(&quot;   Payload: ${extractedData.payload.take(100)}...&quot;)

// Return
def response = [
    &quot;Verification Results&quot;: [
        &quot;Submission ID&quot;: extractedData.submission_id,
        &quot;Status&quot;: extractedData.status,
        &quot;Timestamp&quot;: extractedData.timestamp,
        &quot;Output&quot;: extractedData.parsed_output ?: &quot;Raw: ${extractedData.output.take(50)}...&quot;,
        &quot;Payload&quot;: extractedData.parsed_payload ?: &quot;Raw: ${extractedData.payload.take(50)}...&quot;
    ]
]

return JsonOutput.prettyPrint(JsonOutput.toJson(response))
</stringProp>
        </JSR223Sampler>
        <hashTree/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="RP_Listing_Quality_Report">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\My D\Property Finder Quality Listing\PF_Load_Test\JMeter_Results\Results_tree.csv</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SimpleDataWriter" testclass="ResultCollector" testname="Simple Data Writer">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\My D\Property Finder Quality Listing\PF_Load_Test\JMeter_Results\result.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
