import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../")))
from app.img_process.quality.services.validation_attributes import ValidationAttributeDetails

import pytest
import numpy as np
import cv2
from app.img_process.quality.services.image_quality import calculate_brightness, calculate_sharpness, calculate_distortion, calculate_resolution, image_quality, get_boxed
from unittest.mock import patch, MagicMock
from io import BytesIO
from PIL import Image

def test_calculate_brightness_valid_input():
    # Create a valid grayscale image (mid brightness)
    gray = np.full((100, 100), 127, dtype=np.uint8)
    score = calculate_brightness(gray)
    
    assert score is not None
    assert isinstance(score, float)
    assert score == pytest.approx(10.0, rel=1e-2)

def test_calculate_brightness_dark_image():
    gray = np.full((100, 100), 50, dtype=np.uint8)
    score = calculate_brightness(gray)

    assert score is not None
    assert 0 <= score <= 10

def test_calculate_brightness_none_input():
    score = calculate_brightness(None)
    assert score is None

def test_calculate_brightness_non_numpy_input():
    score = calculate_brightness("not an array")
    assert score is None

def test_calculate_brightness_empty_array():
    gray = np.array([], dtype=np.uint8)
    score = calculate_brightness(gray)
    assert score is None



def test_calculate_sharpness_blurry_image():
    # A flat image has no edges, Laplacian variance will be 0
    gray = np.full((100, 100), 50, dtype=np.uint8)

    score = calculate_sharpness(gray)

    # With current logic, we expect None for laplacian_var == 0
    assert score is None

def test_calculate_sharpness_none_input():
    score = calculate_sharpness(None)
    assert score is None

def test_calculate_sharpness_non_numpy_input():
    score = calculate_sharpness("not an array")
    assert score is None

def test_calculate_sharpness_empty_array():
    gray = np.array([], dtype=np.uint8)
    score = calculate_sharpness(gray)
    assert score is None


def test_calculate_distortion_no_lines():
    # Create a blank image (no lines, maximum distortion)
    gray = np.zeros((100, 100), dtype=np.uint8)
    score = calculate_distortion(gray)

    assert score == 10  # No distortion detected, should return the maximum score

def test_calculate_distortion_none_input():
    score = calculate_distortion(None)
    assert score is None

def test_calculate_distortion_non_numpy_input():
    score = calculate_distortion("not an array")
    assert score is None

def test_calculate_distortion_empty_array():
    gray = np.array([], dtype=np.uint8)
    score = calculate_distortion(gray)
    assert score is None

def test_calculate_resolution_valid_input():
    # Create an image with a known resolution
    image = np.zeros((800, 1200), dtype=np.uint8)  # 1200x800 resolution
    score = calculate_resolution(image)

    assert score is not None
    assert isinstance(score, float)
    assert 0 <= score <= 10

def test_calculate_resolution_lower_resolution():
    # Create an image with a lower resolution than the reference
    image = np.zeros((400, 600), dtype=np.uint8)  # 600x400 resolution
    score = calculate_resolution(image)

    assert score is not None
    assert isinstance(score, float)
    assert 0 <= score <= 10

def test_calculate_resolution_none_input():
    score = calculate_resolution(None)
    assert score is None

def test_calculate_resolution_non_numpy_input():
    score = calculate_resolution("not an array")
    assert score is None

def test_calculate_resolution_empty_array():
    image = np.array([], dtype=np.uint8)
    score = calculate_resolution(image)
    assert score is None

def test_calculate_resolution_zero_dimension_image():
    # Create an image with zero height or width
    image = np.zeros((0, 1200), dtype=np.uint8)  # Zero height
    score = calculate_resolution(image)
    assert score is None

    image = np.zeros((800, 0), dtype=np.uint8)  # Zero width
    score = calculate_resolution(image)
    assert score is None


def create_mock_response_image():
    # Create a simple black image
    img = Image.new("RGB", (100, 100), color="black")
    img_bytes = BytesIO()
    img.save(img_bytes, format="JPEG")
    img_bytes.seek(0)

    # Mock response object
    mock_resp = MagicMock()
    mock_resp.content = img_bytes.read()
    return mock_resp

@patch("app.img_process.quality.services.image_quality.calculate_brightness", return_value=7)
@patch("app.img_process.quality.services.image_quality.calculate_sharpness", return_value=8)
@patch("app.img_process.quality.services.image_quality.calculate_distortion", return_value=6)
@patch("app.img_process.quality.services.image_quality.calculate_resolution", return_value=9)
def test_image_quality_success(
    mock_resolution, mock_distortion, mock_sharpness, mock_brightness
):
    resp_image = create_mock_response_image()

    result = image_quality(resp_image)

    assert isinstance(result, dict)
    assert result["brightness_score"] == 7
    assert result["sharpness_score"] == 8
    assert result["distortion_score"] == 6
    assert result["resolution_score"] == 9

    # Total quality should match the weighted formula
    expected_total = round((7 * 0.15) + (8 * 0.4) + (6 * 0.3) + (9 * 0.15), 2)
    assert result["total_quality"] == expected_total

def test_image_quality_invalid_input_none():
    result = image_quality(None)
    assert result == {}

def test_image_quality_invalid_content_attr():
    class Dummy:
        pass

    result = image_quality(Dummy())
    assert result == {}

def test_image_quality_empty_image():
    # Mock response with invalid image bytes
    mock_resp = MagicMock()
    mock_resp.content = b"not an image"
    result = image_quality(mock_resp)
    assert result == {}

def test_image_quality_corrupt_image_conversion(monkeypatch):
    # Force `cv2.cvtColor` to raise an exception
    mock_resp = create_mock_response_image()

    monkeypatch.setattr(cv2, "cvtColor", lambda *_: (_ for _ in ()).throw(Exception("Conversion failed")))
    result = image_quality(mock_resp)
    assert result == {}


def create_test_image_with_border(color="white", size=(10, 10), border_width=1):
    """
    Creates a test image with a white or black border.
    """
    img = Image.new("L", size, color=255 if color == "white" else 0)
    center_color = 128  # gray center
    for x in range(border_width, size[0] - border_width):
        for y in range(border_width, size[1] - border_width):
            img.putpixel((x, y), center_color)
    return img


def mock_response_with_image(image: Image.Image):
    buffer = BytesIO()
    image.save(buffer, format="PNG")
    buffer.seek(0)
    response = MagicMock()
    response.status_code = 200
    response.content = buffer.read()
    return response


@patch("app.img_process.quality.services.image_quality.requests.get")
def test_get_boxed_white_border(mock_get):
    image = create_test_image_with_border("white")
    mock_get.return_value = mock_response_with_image(image)

    result = get_boxed("http://fakeurl.com/image.png")
    assert result is True


@patch("app.img_process.quality.services.image_quality.requests.get")
def test_get_boxed_black_border(mock_get):
    image = create_test_image_with_border("black")
    mock_get.return_value = mock_response_with_image(image)

    result = get_boxed("http://fakeurl.com/image.png")
    assert result is True


@patch("app.img_process.quality.services.image_quality.requests.get")
def test_get_boxed_no_border(mock_get):
    # All gray (no white/black border)
    image = Image.new("L", (10, 10), color=128)
    mock_get.return_value = mock_response_with_image(image)

    result = get_boxed("http://fakeurl.com/image.png")
    assert result is False


@patch("app.img_process.quality.services.image_quality.requests.get")
def test_get_boxed_invalid_status(mock_get):
    mock_resp = MagicMock()
    mock_resp.status_code = 404
    mock_get.return_value = mock_resp

    result = get_boxed("http://fakeurl.com/image.png")
    assert result is False


@patch("app.img_process.quality.services.image_quality.requests.get", side_effect=Exception("Boom"))
def test_get_boxed_exception(mock_get):
    result = get_boxed("http://fakeurl.com/image.png")
    assert result is False