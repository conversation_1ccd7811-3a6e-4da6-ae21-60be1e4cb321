name: Upload-To-S3

on:
  issue_comment:
    types: [created]

permissions:
  id-token: write # required to use OIDC authentication
  contents: read # required to checkout the code from the repo

jobs:
  upload-to-s3:
    # Only run this job when a comment contains .upload
    if: ${{ github.event.issue.pull_request && contains(github.event.comment.body, '.upload staging') }}
    name: Upload Config and Prompts to S3
    environment: staging
    runs-on: codebuild-${{ github.event.repository.name }}-deploy-staging-${{ github.run_id }}-${{ github.run_attempt }}
    env:
      AWS_REGION: "ap-southeast-1"
      S3_BUCKET: "listing-quality-general-artifacts-staging"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      # AWS credentials will be automatically configured via the runner environment
      
      - name: Upload config to S3
        run: |
          if [ -d "app/config" ]; then
            echo "Uploading config directory to S3..."
            aws s3 cp app/config/ "s3://${{ env.S3_BUCKET }}/config/" --recursive
            echo "Config upload complete"
          else
            echo "Warning: app/config directory not found"
          fi
      
      - name: Upload prompts to S3
        run: |
          if [ -d "app/prompts" ]; then
            echo "Uploading all prompts to S3..."
            aws s3 cp app/prompts/ "s3://${{ env.S3_BUCKET }}/prompts/" --recursive
            echo "Prompts upload complete"
          else
            echo "Warning: app/prompts directory not found"
          fi
      
      - name: Verify S3 Upload
        run: |
          echo "Verifying uploads..."
          echo "Config directory contents:"
          aws s3 ls "s3://${{ env.S3_BUCKET }}/config/"
          echo "Prompts directory contents:"
          aws s3 ls "s3://${{ env.S3_BUCKET }}/prompts/"