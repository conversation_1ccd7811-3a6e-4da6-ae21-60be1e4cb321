from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional


# Base schema for application logs
class AppLogsBase(BaseModel):
    api_name: str
    log_type: str
    description: str
    status: bool
    request_start_time: datetime
    request_end_time: Optional[datetime] = None

# Schema for creating application logs (inherits all fields from AppLogsBase)
class AppLogsCreate(AppLogsBase):
    pass

# Schema for reading application logs (includes unique ID)
class AppLogs(AppLogsBase):
    id: int

    class Config:
        from_attributes = True

# Base schema for error logs
class AppErrorLogsBase(BaseModel):
    api_name: str
    error_type: str
    description: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

# Schema for creating error logs (inherits all fields from AppErrorLogsBase)
class AppErrorLogsCreate(AppErrorLogsBase):
    pass

# Schema for reading error logs (includes unique ID)
class AppErrorLogs(AppErrorLogsBase):
    id: int

    class Config:
        from_attributes = True

# Generic response model
class ResponseModel(BaseModel):
    """Response Object"""

    status: int = Field(...)
    message: str = Field(...)
    result_data: dict = {}

# Validation schema for property listings
class Validate(BaseModel):
    listing_id: int
    country_code: str
    location_path_name_primary: str
    offering_type: str
    property_type: str
    completion_status: str
    bedroom: int
    bathroom: int
    property_sqft: int
    permit_number: int
    property_price: int
    unit_number: int

# Response model for detailed extraction results
class ResponseModelDetailExtraction(BaseModel):
    """Response Object"""

    status: int = Field(...)
    message: str = Field(...)
    result_data: dict = {}
    failed_records: list

# Response model for quality validation results
class ResponseModelQualityValidate(BaseModel):
    """Response Object"""

    status: int = Field(...)
    message: str = Field(...)
    result_data: dict = {}
    failed_records: list

# Response model for detailed validation results
class ResponseModelDetailValidation(BaseModel):
    """Response Object"""

    status: int = Field(...)
    message: str = Field(...)
    result_data: list = [Validate]
    failed_records: list

# Response model for image enhancement results
class ResponseModelImgEnhancement(BaseModel):
    """Response Object"""

    status: int = Field(...)
    message: str = Field(...)
    payload: str
