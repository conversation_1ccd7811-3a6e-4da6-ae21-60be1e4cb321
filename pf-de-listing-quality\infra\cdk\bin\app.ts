#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { ListingQualityLambdaStack } from '../lib/stack';

const app = new cdk.App();

// Get environment from context or environment variable
const deploymentEnv = app.node.tryGetContext('env') || process.env.DEPLOYMENT_ENV || 'staging';
// Get image tag from context if available
const imageTag = app.node.tryGetContext('imageTag');

// Define account configurations
const accountConfigs = {
  staging: {
    accountId: '************',
    region: 'ap-southeast-1'
  },
  production: {
    accountId: '************',
    region: 'ap-southeast-1'
  }
};

// Select the appropriate configuration
const config = accountConfigs[deploymentEnv as keyof typeof accountConfigs] || accountConfigs.staging;

// Create the stack
new ListingQualityLambdaStack(app, `ListingQuality${deploymentEnv === 'production' ? 'Production' : 'Staging'}Stack`, {
  env: {
    account: config.accountId,
    region: config.region
  },
  envName: deploymentEnv, // Pass the environment to the stack using the renamed property
  imageTag: imageTag // Pass the image tag to the stack
});

// Add tags to all resources in the app
cdk.Tags.of(app).add('Application', 'ListingQuality');
cdk.Tags.of(app).add('Environment', deploymentEnv);