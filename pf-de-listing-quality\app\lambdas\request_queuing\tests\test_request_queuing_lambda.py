import json
import os
import pytest
from unittest.mock import patch, MagicMock
from moto import mock_aws
import boto3

os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
os.environ["ENV"] = "staging"
ENV = os.environ["ENV"]

mock_ssm_client = MagicMock()
with patch('boto3.client', return_value=mock_ssm_client):
    import request_queuing

@pytest.fixture
def mock_ssm():
    """Create a mock SSM client."""
    # Reset the mock before each test
    mock_ssm_client.reset_mock()
    return mock_ssm_client

@pytest.fixture
def aws_env_setup(monkeypatch):
    monkeypatch.setenv("ENV", "test")
    monkeypatch.setenv("DIAMOND_QUEUE_URL", "https://sqs.us-east-1.amazonaws.com/123456789012/diamond")
    monkeypatch.setenv("RUBY_QUEUE_URL", "https://sqs.us-east-1.amazonaws.com/123456789012/ruby")
    monkeypatch.setenv("SAPPHIRE_QUEUE_URL", "https://sqs.us-east-1.amazonaws.com/123456789012/sapphire")
    monkeypatch.setenv("EMERALD_QUEUE_URL", "https://sqs.us-east-1.amazonaws.com/123456789012/emerald")


@mock_aws
def test_lambda_handler_valid_message(aws_env_setup):
    # Setup SSM Parameters
    ssm = boto3.client('ssm', region_name="us-east-1")
    ssm.put_parameter(Name=f"/listing-quality/{ENV}/s3/tracking", Value="test-bucket", Type="String")
    ssm.put_parameter(Name=f"/listing-quality/{ENV}/dynamodb/table", Value="ValidationTable", Type="String")
    ssm.put_parameter(Name=f"/listing-quality/{ENV}/dynamodb/ttl", Value="3600", Type="String")
    ssm.put_parameter(Name=f"/listing-quality/{ENV}/queue/notification", Value="https://sqs.us-east-1.amazonaws.com/123456789012/notify", Type="String")

    # Setup DynamoDB
    dynamodb = boto3.client('dynamodb', region_name="us-east-1")
    dynamodb.create_table(
        TableName="ValidationTable",
        KeySchema=[{'AttributeName': 'RequestID', 'KeyType': 'HASH'}],
        AttributeDefinitions=[{'AttributeName': 'RequestID', 'AttributeType': 'S'}],
        BillingMode="PAY_PER_REQUEST"
    )

    # Setup S3 bucket
    s3 = boto3.client('s3', region_name="us-east-1")
    s3.create_bucket(Bucket="test-bucket")

    # Setup SQS queues
    sqs = boto3.client("sqs", region_name="us-east-1")
    queue_urls = {}
    for q in ["diamond", "ruby", "sapphire", "emerald", "notify"]:
        response = sqs.create_queue(QueueName=q)
        queue_urls[q] = response["QueueUrl"]

    test_event = {
        "Records": [
            {
                "messageId": "abc123",
                "body": json.dumps({
                    "submission_id": "sub-001",
                    "customer_type": "diamond",
                    "type": "test-api",
                    "listing_id": 123,
                    "country_code": "US",
                    "images": ["img1.jpg"]
                })
            }
        ]
    }

    # Re-import to re-trigger SSM param fetch
    import importlib
    importlib.reload(request_queuing)

    result = request_queuing.lambda_handler(test_event, {})
    assert result["statusCode"] == 200
    body = json.loads(result["body"])
    assert "message" in body
