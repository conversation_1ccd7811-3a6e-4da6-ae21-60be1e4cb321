import time
import json
from fastapi import APIRouter
from app.img_process.quality.schema import PhotoAttributes
from app.schema.schema import ResponseModelQualityValidate
from app.img_process.quality.quality_validation import (
    validate_img_quality,
    process_parameters,
)
from core.utils.utils import (
    attributes_validations,
    delete_message_from_request_processing_queue,
    put_message_in_notification_success_queue,
    get_listing_record_from_dynamodb,
    filter_already_processed_images,
    add_listing_record_to_dynamodb,
    get_current_timestamp,
    get_ttl_timestamp,
    load_parameters,
    update_llm_cost,
    calculate_llm_usage_metrics,
    convert_floats_to_decimals,
)

from core.enums.status import ResponseStatus
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_warning,
    log_frame_error,
)


logger = get_logger(__name__)

quality_router = APIRouter()


def log_and_handle_validation_error(attributes, error_message, request_payload):
    """
    Helper function to log validation error, add to DynamoDB, and process the notification queue.
    """

    # Construct the payload for validation failure to store in DynamoDB
    validation_error_payload = {
        "api_type": request_payload.get("type"),  # Type of the API request
        "customer_type": attributes.customer_type,  # Type of customer making the request
        "status": ResponseStatus.FAIL_VALIDATION.value,  # Set status as validation failure
        "payload": json.dumps(
            request_payload
        ),  # Original request data (as JSON string)
        "output": json.dumps(
            {"error_message": error_message}
        ),  # Error message to be returned
        "timestamp": get_current_timestamp(),  # Current timestamp for logging
        "ttl": get_ttl_timestamp(),  # TTL for the record to expire in DynamoDB
    }

    # Proceed only if submission_id is available
    if attributes.submission_id:
        add_listing_record_to_dynamodb(
            attributes.submission_id, validation_error_payload
        )

        # Log the validation error event for tracking
        log_frame_info(
            logger,
            message="Processing notification queue for Request Id due to validation failure",
            submission_id=attributes.submission_id,
        )

        # Push the validation error to a success notification queue (used for alerting systems)
        put_message_in_notification_success_queue(
            attributes.submission_id, {"error_message": error_message}
        )

        # Remove the current request message from the processing queue as it is invalid
        delete_message_from_request_processing_queue(
            attributes.submission_id, attributes.message_handle
        )

    # Return a standard response model for validation failure
    return ResponseModelQualityValidate(
        status=400,  # HTTP 400 Bad Request for validation issues
        message=error_message,  # Validation error message
        result_data={},  # No data to return in case of failure
        failed_records=[],  # No failed records to return (used when applicable)
    )


@quality_router.post("", tags=["Validate Quality of Image"])
async def validate_quality(attributes: PhotoAttributes):
    # Log the initial request for image quality validation with detailed info
    log_frame_info(
        logger,
        message="Received request for image quality validation",
        listing_id=attributes.listing_id,
        submission_id=attributes.submission_id,
        country_code=attributes.country_code,
        message_handle=attributes.message_handle,
        validate_params=attributes.validate_params,
        num_images=len(attributes.images) if attributes.images else 0,
        custom_input_list=attributes.custom_input_list,
        customer_type=attributes.customer_type,
    )

    try:
        # Start tracking execution time
        execution_start_time = time.time()
        processing_time = []

        # Convert the PhotoAttributes object into a JSON-serializable dictionary
        request_payload = attributes.model_dump(mode="json")

        # Run basic attribute-level validations
        error_message = attributes_validations(attributes)
        if error_message:
            log_frame_warning(logger, message="Validation failed", error=error_message)
            return log_and_handle_validation_error(
                attributes, error_message, request_payload
            )

        # Load global parameter configuration
        parameters = load_parameters()

        # Validate the parameters received in the request
        if attributes.validate_params:
            if 0 in attributes.validate_params:
                # Invalid parameter detected
                log_frame_warning(
                    logger, message="Invalid input for validating parameters"
                )
                return log_and_handle_validation_error(
                    attributes,
                    "Invalid Input for validating parameters",
                    request_payload,
                )

            # Select only the parameters required for validation
            validate_attr = [
                param
                for param in parameters
                if param["id"] in attributes.validate_params
            ]
        else:
            # Use all parameters if none were specified
            validate_attr = parameters

        if not validate_attr:
            # No valid parameters found for validation
            log_frame_warning(logger, message="No validating parameters found")
            return log_and_handle_validation_error(
                attributes, "Validating Parameters not found", request_payload
            )

        # Validate custom input values if provided
        if attributes and attributes.custom_input_list:
            for key, value in attributes.custom_input_list.items():
                if isinstance(value, list):
                    # Check if list is empty
                    log_frame_warning(logger, message="Custom input is empty", key=key)
                    if not value:
                        log_frame_warning(
                            logger, message="Custom input value found is empty", key=key
                        )
                        return log_and_handle_validation_error(
                            attributes,
                            f"{key}: given as empty. Values for custom inputs cannot be Empty, Please check the payload.",
                            request_payload,
                        )
                else:
                    # Custom input is not a list (invalid format)
                    return log_and_handle_validation_error(
                        attributes,
                        f"{key}: Value for custom inputs is Not Valid, Please check the payload.",
                        request_payload,
                    )

        # Retrieve any already-processed images to avoid reprocessing
        processed_images = []
        if attributes.submission_id:
            dynamodb_record = get_listing_record_from_dynamodb(attributes.submission_id)
            if dynamodb_record:
                processed_images, attributes.images = filter_already_processed_images(
                    dynamodb_record, attributes.images
                )
                # Carry over any existing processing times
                processing_time = (
                    dynamodb_record.get("processing_time", [])
                    if dynamodb_record.get("processing_time")
                    else []
                )

        # Prepare the input data for image quality validation
        photo_attributes_data = {
            "country_code": attributes.validate_params,
            "listing_id": attributes.listing_id,
            "images": attributes.images,
        }

        log_frame_info(logger, "Processing image quality validation")

        # Enable each parameter for validation by name
        validate_attr_mapping = {param["param_name"]: True for param in validate_attr}
        photo_attributes_data.update(validate_attr_mapping)

        # Format parameters for the validator function
        processed_params = process_parameters(validate_attr)

        # Perform the image quality validation
        response_images, fail_rec, throttled_records = validate_img_quality(
            photo_attributes_data,
            processed_params,
            attributes.custom_input_list,
            attributes.submission_id,
        )

        # Combine newly processed and previously processed images
        success_records = response_images + processed_images

        # Update cost based on successful records
        update_llm_cost(success_records)

        # Build the response dictionary
        resp = {
            "submission_id": attributes.submission_id,
            "country_code": attributes.country_code,
            "listing_id": attributes.listing_id,
            "customer_type": attributes.customer_type,
            "validate_params": attributes.validate_params,
            "images": success_records,
        }

        # Ensure floats are converted to Decimal for DynamoDB compatibility
        convert_floats_to_decimals(resp)

        # Append any failed or throttled results to the response
        if fail_rec:
            resp["failed_records"] = fail_rec
        if throttled_records:
            resp["throttled_records"] = throttled_records

        # Calculate and store total processing time
        execution_end_time = time.time()
        total_execution_time = execution_end_time - execution_start_time
        processing_time.append(
            {"run": len(processing_time) + 1, "time": str(total_execution_time)}
        )
        logger.info(
            f"Total execution time: {total_execution_time} for Request Id: {attributes.submission_id}"
        )

        # Estimate LLM usage metrics
        llm_usage = calculate_llm_usage_metrics(success_records)

        # Save updated record into DynamoDB
        if attributes.submission_id:
            request_payload.pop("message_handle", None)
            dynamodb_payload = {
                "api_type": request_payload.get("type"),
                "status": ResponseStatus.PENDING.value
                if throttled_records
                else ResponseStatus.SUCCESS.value
                if success_records
                else ResponseStatus.FAIL.value,
                "payload": json.dumps(request_payload),
                "output": json.dumps(resp, ensure_ascii=False),
                "timestamp": get_current_timestamp(),
                "ttl": get_ttl_timestamp(),
                "processing_time": processing_time,
                "llm_usage": convert_floats_to_decimals(llm_usage),
            }
            add_listing_record_to_dynamodb(attributes.submission_id, dynamodb_payload)

        # Merge throttled records into failed list
        fail_rec = fail_rec + throttled_records

        # Clean up response before sending it back
        resp.pop("failed_records", None)
        resp.pop("throttled_records", None)

        # If throttled, raise an error with details
        if throttled_records:
            log_frame_error(
                logger,
                message="Throttle error detected",
                extra={
                    "throttled_records": throttled_records,
                    "error_message": [
                        record["message"] for record in throttled_records
                    ],  # Log full messages
                },
            )
            raise Exception(
                "Bedrock throttling error occurred: " + str(throttled_records)
            )

        # Send response to notification queue and clean up message from processing queue
        if attributes.submission_id:
            log_frame_info(
                logger,
                message="Processing notification queue for Request Id",
                submission_id=attributes.submission_id,
            )
            put_message_in_notification_success_queue(
                attributes.submission_id,
                {
                    "result_data": resp,
                    "failed_records": fail_rec,
                    "llm_usage": llm_usage,
                },
            )
            delete_message_from_request_processing_queue(
                attributes.submission_id, attributes.message_handle
            )

        # Final log before returning
        log_frame_info(logger, "Image quality validation completed successfully")

        # Return response model with success
        return ResponseModelQualityValidate(
            status=200,
            message="Request Processed Successfully",
            result_data=resp,
            failed_records=fail_rec,
        )
    except Exception as e:
        # Handle unexpected errors and log stack trace
        log_frame_error(logger, str(e))
        raise Exception("Listing Quality processing error occurred: " + str(e))
