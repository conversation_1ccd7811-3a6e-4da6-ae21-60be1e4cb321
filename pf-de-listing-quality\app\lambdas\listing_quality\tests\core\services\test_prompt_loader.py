import pytest
from unittest.mock import MagicMock

from core.services.prompt_loads import PromptLoader


@pytest.mark.asyncio
async def test_prompt_loader_s3_download(s3_prompts):
    PromptLoader._prompt_contents = {}  # Reset cache
    await PromptLoader.load_prompts(s3_prompts)

    result = PromptLoader.get_prompt("detect_watermark_text")
    assert result == "This is a sample prompt text for detection."
    assert "detect_watermark_text" in PromptLoader._prompt_contents


def test_get_prompt_empty_cache():
    PromptLoader._prompt_contents = {}
    result = PromptLoader.get_prompt("anything")
    assert result == "Error: Prompts not loaded."


def test_get_prompt_not_found():
    PromptLoader._prompt_contents = {"some_key": "value"}
    result = PromptLoader.get_prompt("not_found_key")
    assert result == "Error: Prompt 'not_found_key' not found."


@pytest.mark.asyncio
async def test_load_prompts_invalid_json(tmp_path):
    # Write invalid JSON to file
    bad_file = tmp_path / "bad.json"
    bad_file.write_text("{invalid: json,,}")

    await PromptLoader.load_prompts(str(bad_file))
    assert PromptLoader._prompts == {}  # Should reset to empty dict


@pytest.mark.asyncio
async def test_download_all_prompts_missing_bucket(monkeypatch):
    PromptLoader._prompts = {"foo": "bar.txt"}
    PromptLoader._prompt_contents = {}

    fake_config = MagicMock()
    fake_config.get.return_value = None  # Simulate missing bucket

    # Patch the entire config object in the module
    monkeypatch.setattr("core.services.prompt_loads.config", fake_config)

    await PromptLoader._download_all_prompts()
    assert PromptLoader._prompt_contents == {}  # Should skip downloading


@pytest.mark.asyncio
async def test_skip_already_downloaded(monkeypatch):
    PromptLoader._prompts = {"foo": "bar.txt"}
    PromptLoader._prompt_contents = {"foo": "already here"}

    # Patch S3 client to check if it's called
    mock_download = MagicMock()
    monkeypatch.setattr(PromptLoader, "_download_single_prompt", mock_download)

    await PromptLoader._download_all_prompts()
    mock_download.assert_not_called()
