import yaml
import boto3

from config.config import config  # Import config object if needed later
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_warning,
    log_frame_error,
)

config = config.config

# Initialize S3 client to interact with AWS S3
s3_client = boto3.client("s3")

# Initialize logger for logging events and errors
logger = get_logger(__name__)


# Retrieve the config.yaml file from S3
class PreConfigParameters:
    def retrieve_values_from_yaml(self):
        # Retrieve S3 bucket details from configuration
        bucket_name = config.get("s3_bucket_artifacts")
        bucket_dir = config.get("s3_bucket_artifacts_config")

        # Define the S3 key for the config.yaml file
        yaml_file_key = f"{bucket_dir}/config.yaml"

        # Log info about the attempt to fetch from S3
        log_frame_info(
            logger,
            message="Attempting to fetch from S3 bucket",
            yaml_file_key=yaml_file_key,
            bucket_name=bucket_name,
        )

        # Try to fetch the YAML file from the S3 bucket
        try:
            response = s3_client.get_object(Bucket=bucket_name, Key=yaml_file_key)
            # Read the file content and load it as YAML
            yaml_content = response["Body"].read()  # Read the file content
            config_data = yaml.safe_load(yaml_content)  # Parse the YAML content

            # Log successful fetch and parse operation
            log_frame_info(
                logger,
                message="Successfully fetched and parsed config.yaml from /",
                bucket_name=bucket_name,
                yaml_file_key=yaml_file_key,
            )

            return config_data  # Return parsed config data
        except Exception as e:
            # Log error in case fetching or parsing fails
            log_frame_error(
                logger,
                message="Error fetching config.yaml from S3",
                error=str(e),
                exc_info=True,
            )
            return None

    def get_quality_validation_secrets(self):
        # Retrieve the YAML config data
        config_data = self.retrieve_values_from_yaml()

        if not config_data:
            # Log error if config data retrieval fails
            log_frame_error(
                logger, message="Failed to retrieve YAML configuration data."
            )
            return None  # Return None if data retrieval fails

        # Get the 'QUALITY_VALIDATION_CONFIG_PARAMETERS' section from config data
        pre_configs = config_data.get("QUALITY_VALIDATION_CONFIG_PARAMETERS", None)

        if pre_configs is None:
            # Log a warning if the section is not found in the YAML
            log_frame_warning(
                logger,
                message="QUALITY_VALIDATION_CONFIG_PARAMETERS not found in the YAML file.",
            )
            return None  # Return None if section not found

        # Log success when section is found
        log_frame_info(
            logger,
            message="Successfully retrieved QUALITY_VALIDATION_CONFIG_PARAMETERS.",
        )
        pre_configs = config_data["QUALITY_VALIDATION_CONFIG_PARAMETERS"]
        return pre_configs
