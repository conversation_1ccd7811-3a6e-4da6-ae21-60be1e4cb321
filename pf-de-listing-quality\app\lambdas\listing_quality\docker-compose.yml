version: "3.8"

services:
  app:
    build:
      context: .  # Current directory as build context
      dockerfile: Dockerfile-local  # Specify the custom Dockerfile name
    ports:
      - "8000:8000"  # Map container port 8000 to host port 8000
    volumes:
      - .:/app  # Mount current directory to /app inside the container
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - CONCURRENT_WORKERS=5
      - AWS_DEFAULT_REGION_PARAM=AWS_DEFAULT_REGION
      - DEFAULT_REGION_VALUE=us-east-1
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    # Or you can use the python command instead of uvicorn
    # command: ["python3", "main.py"]

