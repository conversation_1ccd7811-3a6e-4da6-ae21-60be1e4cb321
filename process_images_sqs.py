import csv
import json
import random
import pandas as pd
import boto3
import os
from datetime import datetime
from typing import List, Dict, Any

def create_request_payload(
    listing_id: int,
    country_code: str,
    image_urls: List[str],
    submission_id: str = None,
    validate_params: List[str] = None,
    customer_type: str = "Ruby"
) -> Dict[str, Any]:
    """
    Create the request payload for the listing quality endpoint.
    
    Args:
        listing_id: The ID of the listing
        country_code: The country code (e.g., 'AE')
        image_urls: List of image URLs to validate
        submission_id: Unique ID for this submission
        validate_params: Optional list of validation parameters
        customer_type: Customer type identifier (default: Ruby)
        
    Returns:
        Dictionary containing the formatted request payload
    """
    # Default custom input list
    custom_input_list = {
        "photo_classification": [
            "Open kitchen", "Closed kitchen", "dining room", "bedroom", 
            "bathroom", "brochure", "floor plan", "map", "logo", 
            "living room", "garden", "balcony", "building exterior", 
            "parking", "MaidRoom", "laundry", "Corridor", "multiple_images"
        ],
        "room_category": ["Luxury", "Modern", "Classic"],
        "room_size": ["small", "medium", "large"]
    }
    
    # Create images list with IDs
    images = [{"id": i, "src": url} for i, url in enumerate(image_urls)]
    
    # Create the body payload according to expected SQS structure
    body = {
        "customer_type": customer_type,
        "type": "IMAGE_VALIDATION",
        "listing_id": listing_id,
        "country_code": country_code.lower(),  # Ensure country code is lowercase
        "submission_id": submission_id or str(listing_id),  # Use listing_id as fallback if submission_id is not provided
        "validate_params": validate_params or [],
        "images": images,
        "custom_input_list": custom_input_list
    }
    
    return body

def send_to_sqs_queue(
    sqs_client,
    queue_url: str,
    payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Send a message to the SQS queue.
    
    Args:
        sqs_client: Boto3 SQS client
        queue_url: URL of the SQS queue
        payload: Message payload to send
        
    Returns:
        Response from SQS
    """
    try:
        # Validate JSON before sending
        message_body = json.dumps(payload)
        # Verify the JSON is valid by parsing it back
        json.loads(message_body)
        
        # Debug print to verify JSON structure
        print(f"Sending valid JSON payload: {message_body[:200]}...")
        
        response = sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=message_body
        )
        return response
    except json.JSONDecodeError as je:
        print(f"Invalid JSON format: {str(je)}")
        print(f"Problematic payload: {payload}")
        return {"error": f"Invalid JSON: {str(je)}"}
    except Exception as e:
        print(f"Error sending message to SQS: {str(e)}")
        return {"error": str(e)}

def process_images_in_batches(
    csv_file_path: str,
    queue_url: str,
    batch_size: int = 10,
    country_code: str = "AE"
) -> None:
    """
    Process images from CSV in batches, send them to SQS queue,
    and save property IDs back to a new CSV file.
    
    Args:
        csv_file_path: Path to the CSV file containing image URLs
        queue_url: URL of the SQS queue
        batch_size: Number of images to process in each batch
        country_code: Country code for the requests
    """
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    
    # Extract image URLs from the PROPERTY_IMAGE_URL column
    image_urls = df['PROPERTY_IMAGE_URL'].tolist()
    
    # Create batches of image URLs
    batches = [image_urls[i:i + batch_size] for i in range(0, len(image_urls), batch_size)]
    
    # Initialize AWS SQS client with cached credentials
    sqs_client = boto3.client('sqs', region_name='ap-southeast-1')
    
    # Prepare results
    results = []
    
    print(f"Processing {len(batches)} batches of images...")
    
    # Process each batch
    for i, batch in enumerate(batches):
        # Generate a random property ID for this batch
        property_id = random.randint(10000000, 99999999)
        
        print(f"Batch {i+1}/{len(batches)}: Processing {len(batch)} images with property ID {property_id}")
        
        # Filter out any None or empty URLs
        valid_urls = [url for url in batch if url and isinstance(url, str)]
        
        if valid_urls:
            # Create a unique submission ID for this batch
            submission_id = f"submission_{property_id}_{int(datetime.now().timestamp())}"
            
            # Create the request payload with the required structure
            payload = create_request_payload(
                listing_id=property_id,
                country_code=country_code,
                image_urls=valid_urls,
                submission_id=submission_id,
                customer_type="Ruby",
                validate_params=[]
            )
            
            # Send to SQS queue
            response = send_to_sqs_queue(sqs_client, queue_url, payload)
            
            # Store batch information with property ID
            for j, url in enumerate(valid_urls):
                # Find the original row index in the dataframe
                original_index = df.index[df['PROPERTY_IMAGE_URL'] == url].tolist()
                original_index = original_index[0] if original_index else None
                
                # Get additional information from the original CSV if index was found
                additional_info = {}
                if original_index is not None:
                    for col in df.columns:
                        if col != 'PROPERTY_IMAGE_URL':
                            additional_info[col] = df.at[original_index, col]
                
                image_result = {
                    "batch_number": i+1,
                    "property_id": property_id,
                    "submission_id": submission_id,
                    "image_url": url,
                    "image_index": j,
                    "sqs_message_id": response.get("MessageId", "Error"),
                    **additional_info
                }
                
                results.append(image_result)
        
        # Add a small delay between batches
        import time
        time.sleep(0.1)
    
    # Create a DataFrame from the results
    results_df = pd.DataFrame(results)
    
    # Generate output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(os.path.dirname(csv_file_path), f"sqs_results_{timestamp}.csv")
    
    # Save results to CSV
    results_df.to_csv(output_file, index=False)
    print(f"Results saved to {output_file}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Process images from CSV in batches and send to SQS queue")
    parser.add_argument("--csv", required=True, help="Path to the CSV file containing image URLs")
    parser.add_argument("--queue-url", required=True, help="URL of the SQS queue")
    parser.add_argument("--batch-size", type=int, default=10, help="Number of images to process in each batch")
    parser.add_argument("--country-code", default="AE", help="Country code for the requests")
    
    args = parser.parse_args()
    
    process_images_in_batches(
        csv_file_path=args.csv,
        queue_url=args.queue_url,
        batch_size=args.batch_size,
        country_code=args.country_code
    )
