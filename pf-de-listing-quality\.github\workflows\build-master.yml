name: Build-Master

on:
  push:
    branches:
      - master

permissions:
  id-token: write # required to use OIDC authentication
  contents: read # required to checkout the code from the repo

env:
  IMAGE_REGISTRY: "995500545255.dkr.ecr.ap-southeast-1.amazonaws.com/pf-de-listing-quality"

jobs:
  Build-master:
    name: Build-Master-Image
    runs-on: codebuild-${{ github.event.repository.name }}-build-staging-${{ github.run_id }}-${{ github.run_attempt }}
    steps:
      # Checkout Code
      - name: Chekout code
        id: checkout
        uses: actions/checkout@v4

      #  Logging into AWS ECR
      - name: Login into AWS ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      # Creating Commit ID
      - name: Construct Commit ID
        id: create-commit-id
        run: |
          temp=$(echo ${{ github.sha }} | head -c 10)
          echo "DOCKER_IMAGES_TAG_COMMITID=$temp" >> $GITHUB_ENV
      
      # Debug filesystem structure
      - name: Debug directory structure
        id: debug
        run: |
          # Show current directory
          pwd
          # List current directory contents
          ls -la
          # Show all directories under the current one
          find . -type d -maxdepth 3
      
      - name: Build & push Image
        id: build-push-image
        run: |
          cd ./app/lambdas/listing_quality/
          docker build -t ${{ env.IMAGE_REGISTRY }}:master-$DOCKER_IMAGES_TAG_COMMITID .
          docker push ${{ env.IMAGE_REGISTRY }}:master-$DOCKER_IMAGES_TAG_COMMITID

          docker tag ${{ env.IMAGE_REGISTRY }}:master-$DOCKER_IMAGES_TAG_COMMITID ${{ env.IMAGE_REGISTRY }}:master-${{github.sha}}
          docker push ${{ env.IMAGE_REGISTRY }}:master-${{github.sha}}

          docker tag ${{ env.IMAGE_REGISTRY }}:master-$DOCKER_IMAGES_TAG_COMMITID ${{ env.IMAGE_REGISTRY }}:master-${{github.run_number}}
          docker push ${{ env.IMAGE_REGISTRY }}:master-${{github.run_number}}

          docker tag ${{ env.IMAGE_REGISTRY }}:master-$DOCKER_IMAGES_TAG_COMMITID ${{ env.IMAGE_REGISTRY }}:latest
          docker push ${{ env.IMAGE_REGISTRY }}:latest

      # Update Lambda function with latest image
      - name: Update Quality Listing Lambda function
        id: update-lambda
        run: |
          # Check if Lambda function exists
          if aws lambda get-function --function-name arn:aws:lambda:ap-southeast-1:836411974350:function:listing-quality-quality-listing-lambda-production &>/dev/null; then
            echo "Lambda function found, updating with latest image..."
            aws lambda update-function-code \
              --function-name arn:aws:lambda:ap-southeast-1:836411974350:function:listing-quality-quality-listing-lambda-production \
              --image-uri ${{ env.IMAGE_REGISTRY }}:latest
            echo "Lambda function updated successfully."
          else
            echo "Lambda function not found. This is likely the first run where the Lambda hasn't been created yet."
            echo "The Lambda function will be created by the CDK deployment."
          fi