import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../")))
import pytest
from app.img_process.quality.quality_validation import img_validate_quality, process_parameters, convert_value, process_checks_results, convert_str_to_bool, validate_img_quality
from app.img_process.quality.schema import PhotoAttributes
from unittest.mock import patch, Mock, MagicMock
from core.enums.status import ResponseStatus
from app.img_process.quality.process import ImageQualityAnalysisModel
from pydantic import BaseModel  
from core.logger.logger import logger

class PhotoAttributes(BaseModel):
    quality: str
    type: str


@pytest.fixture
def valid_image():
    image = MagicMock()
    image.src = "valid_image_url"
    return image

@pytest.fixture
def parameters():
    return [
        {"param_name": "param1", "param_prompt": "Prompt for param1", "param_type": "Bool"},
        {"param_name": "param2", "param_prompt": "Prompt for param2", "param_type": "int"},
        {"param_name": "param3", "param_prompt": "Prompt for param3", "param_type": "str"},
        {"param_name": "param4", "param_prompt": "Prompt for param4", "param_type": "str"},
        # Edge case: Missing param_name or param_prompt
        {"param_type": "int"},
        {"param_name": "param6", "param_type": "Bool"},
    ]

@pytest.fixture
def attributes():
    return PhotoAttributes(quality="high", type="portrait")  # Adjust to your actual attributes


@pytest.fixture
def processed_params():
    return {"quality_of_picture": True, "brightness": True}


@pytest.fixture
def other_vals():
    return {"photo_classification": "portrait"}


def test_img_validate_quality_invalid_image(valid_image, attributes, processed_params, other_vals):
    # Simulating an invalid image (missing 'src')
    invalid_image = MagicMock()
    invalid_image.src = None  # 'src' is required, so this should trigger an error
    
    result = img_validate_quality(attributes, invalid_image, processed_params, other_vals)

    assert result["status"] == ResponseStatus.FAIL.value
    assert "Invalid image source." in result["message"]


def test_img_validate_quality_image_processing_failure(valid_image, attributes, processed_params, other_vals):
    # Simulate a failure during image processing (e.g., process_images raises an exception)
    with patch.object(ImageQualityAnalysisModel, "process_images", side_effect=Exception("Simulated image processing error")):
        result = img_validate_quality(attributes, valid_image, processed_params, other_vals)

    assert result["status"] == ResponseStatus.FAIL.value
    assert "Image processing error: Simulated image processing error" in result["message"]


def test_img_validate_quality_valid_case(valid_image, attributes, processed_params, other_vals):
    # Simulate a successful image processing
    mock_check_results = {
        "status": ResponseStatus.SUCCESS.value,
        "message": "Processing completed successfully",
        "data": {
            "quality_of_picture": 85,
            "brightness": 70,
            "photo_classification": "portrait",
            "quality_of_details": {
                "factors_quality": {"sharpness": 80},
                "llm_quality": 88
            }
        }
    }
    with patch.object(ImageQualityAnalysisModel, "process_images", return_value=mock_check_results):
        result = img_validate_quality(attributes, valid_image, processed_params, other_vals)

    assert result["status"] == ResponseStatus.SUCCESS.value
    assert "Processing completed successfully" in result["message"]
    assert "quality_of_picture" in result["data"]
    assert result["data"]["quality_of_picture"] == 85
    assert result["data"]["brightness"] == 70


def test_img_validate_quality_invalid_image_processing_status(valid_image, attributes, processed_params, other_vals):
    # Simulate an image processing result with a failure status
    mock_check_results = {
        "status": ResponseStatus.FAIL.value,
        "message": "Image quality validation failed",
        "data": None
    }
    with patch.object(ImageQualityAnalysisModel, "process_images", return_value=mock_check_results):
        result = img_validate_quality(attributes, valid_image, processed_params, other_vals)

    assert result["status"] == ResponseStatus.FAIL.value
    assert "Image quality validation failed" in result["message"]

def test_process_parameters_valid(parameters):
    # Call the function with the valid parameters
    result = process_parameters(parameters)
    
    # Check if the result contains the correct keys in 'result'
    assert "param1" in result
    assert result["param1"] == "Prompt for param1"
    assert "param2" in result
    assert result["param2"] == "Prompt for param2"
    assert "param3" in result
    assert result["param3"] == "Prompt for param3"
    
    # Check if sample_output is correctly populated
    sample_output = result.get("sample_output")
    assert sample_output is not None
    assert "param1" in sample_output
    assert sample_output["param1"] == "True|False"
    assert "param2" in sample_output
    assert isinstance(sample_output["param2"], int)  # Random integer between 1 and 10
    assert "param3" in sample_output
    assert sample_output["param3"] == "example string"


def test_process_parameters_missing_param_name_or_prompt(parameters):
    # Test for missing param_name or param_prompt
    parameters_with_missing_fields = [
        {"param_name": "param1", "param_type": "Bool"},  # Missing param_prompt
        {"param_prompt": "Prompt for param2", "param_type": "int"},  # Missing param_name
    ]
    
    result = process_parameters(parameters_with_missing_fields)
    
    # Assert the result does not contain the missing values
    assert "param1" not in result
    assert "param2" not in result

def test_process_parameters_random_int_range(parameters):
    # Test that random integers are generated between 1 and 10 for "int" type
    parameters_with_int = [{"param_name": "param5", "param_prompt": "Prompt for param5", "param_type": "int"}]
    
    with patch("random.randint", return_value=5):  # Mocking random.randint to return 5
        result = process_parameters(parameters_with_int)
        assert result["sample_output"]["param5"] == 5


# Sample logger mock to capture the log messages during tests
class MockLogger:
    def info(self, message, **kwargs):
        pass  # Stub out logging methods for the test
    
    def warning(self, message, **kwargs):
        pass  # Stub out logging methods for the test
    
    def error(self, message, **kwargs):
        pass  # Stub out logging methods for the test

@pytest.fixture
def mock_logger():
    return MockLogger()

def test_convert_value_valid_true(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value("True")
        assert result is True

def test_convert_value_valid_false(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value("False")
        assert result is False

def test_convert_value_case_insensitive_true(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value("true")
        assert result is True

def test_convert_value_case_insensitive_false(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value("false")
        assert result is False

def test_convert_value_invalid_string(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value("hello")
        assert result == "hello"

def test_convert_value_none(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value(None)
        assert result is None

def test_convert_value_integer(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value(123)
        assert result == 123

def test_convert_value_other_type(mock_logger):
    with patch("core.logger.logger", mock_logger):
        result = convert_value([1, 2, 3])
        assert result == [1, 2, 3]

def test_convert_value_error_handling(mock_logger):
    with patch("core.logger.logger", mock_logger):
        with patch("app.img_process.quality.quality_validation.convert_value", side_effect=Exception("Conversion error")):
            result = convert_value("True")
            assert result == True  # Return original value on error

@pytest.fixture
def sample_data():
    return {
        "bool_true": "True",
        "bool_false": "False",
        "number": 5,
        "text": "hello",
        "none_value": None,
    }

def test_process_checks_results_valid(sample_data):
    with patch("app.img_process.quality.quality_validation.convert_value", side_effect=lambda v: v if v != "True" else True):
        result = process_checks_results(sample_data)
        assert isinstance(result, dict)
        assert result["bool_true"] is True
        assert result["bool_false"] == "False"
        assert result["number"] == 5
        assert result["text"] == "hello"
        assert result["none_value"] is None

def test_process_checks_results_empty_dict():
    result = process_checks_results({})
    assert result == {}

def test_process_checks_results_invalid_input():
    result = process_checks_results(["invalid", "input"])
    assert result == {}

def test_process_checks_results_with_exception_on_conversion():
    faulty_data = {
        "will_fail": "crash",
        "safe_key": "True"
    }

    def faulty_convert(val):
        if val == "crash":
            raise ValueError("Boom!")
        return True if val == "True" else val

    with patch("app.img_process.quality.quality_validation.convert_value", side_effect=faulty_convert):
        result = process_checks_results(faulty_data)
        assert result["will_fail"] == "crash"  # original value preserved on exception
        assert result["safe_key"] is True

def test_process_checks_results_all_types():
    mixed_data = {
        "true_str": "True",
        "false_str": "False",
        "int_val": 1,
        "none_val": None,
        "list_val": [1, 2],
    }

    with patch("app.img_process.quality.quality_validation.convert_value", side_effect=lambda v: v):
        result = process_checks_results(mixed_data)
        assert result == mixed_data

def test_convert_str_to_bool_valid_data():
    input_data = [
        {"checks_results": {"is_valid": "True"}},
        {"checks_results": {"is_active": "False"}},
    ]

    expected_output = [
        {"checks_results": {"is_valid": True}},
        {"checks_results": {"is_active": False}},
    ]

    with patch("app.img_process.quality.quality_validation.process_checks_results") as mock_process:
        mock_process.side_effect = lambda x: {k: v == "True" for k, v in x.items()}
        result = convert_str_to_bool(input_data)
        assert result == expected_output

def test_convert_str_to_bool_invalid_input_type():
    input_data = "this is not a list"
    result = convert_str_to_bool(input_data)
    assert result == []

def test_convert_str_to_bool_item_not_dict():
    input_data = [{"checks_results": {"a": "True"}}, "invalid", 123]

    with patch("app.img_process.quality.quality_validation.process_checks_results", return_value={"a": True}):
        result = convert_str_to_bool(input_data)
        assert result[0]["checks_results"] == {"a": True}
        assert len(result) == 3  # original input preserved (skips non-dict safely)

def test_convert_str_to_bool_missing_checks_results():
    input_data = [{"no_results": 1}, {"checks_results": {"flag": "True"}}]

    with patch("app.img_process.quality.quality_validation.process_checks_results") as mock_process:
        mock_process.side_effect = lambda x: {k: v == "True" for k, v in x.items()}
        result = convert_str_to_bool(input_data)
        assert "checks_results" not in result[0]
        assert result[1]["checks_results"] == {"flag": True}

def test_convert_str_to_bool_process_checks_results_exception():
    input_data = [{"checks_results": {"raise_error": "True"}}]

    def faulty_processor(data):
        raise ValueError("Boom!")

    with patch("app.img_process.quality.quality_validation.process_checks_results", side_effect=faulty_processor):
        result = convert_str_to_bool(input_data)
        # Should preserve original data on exception
        assert result[0]["checks_results"] == {"raise_error": "True"}


@pytest.fixture
def valid_attributes():
    return {
        "images": [
            MagicMock(id="img1", src="http://example.com/img1.jpg"),
            MagicMock(id="img2", src="http://example.com/img2.jpg"),
        ]
    }

@pytest.fixture
def processed_params():
    return {"quality_of_picture": True, "brightness": True}

@pytest.fixture
def other_vals():
    return {"photo_classification": ["kitchen", "dining room"]}

@pytest.fixture
def submission_id():
    return "test-submission-123"

@patch("core.utils.utils.remove_duplicate_images")
@patch("core.utils.utils.get_images_to_process")
@patch("app.img_process.quality.quality_validation.img_validate_quality")
def test_validate_img_quality_success(
    mock_img_validate_quality,
    mock_get_images_to_process,
    mock_remove_duplicates,
    valid_attributes,
    processed_params,
    other_vals,
    submission_id,
):
    mock_remove_duplicates.return_value = (valid_attributes["images"], [])
    mock_get_images_to_process.return_value = valid_attributes["images"]

    mock_img_validate_quality.return_value = {
        "status": ResponseStatus.SUCCESS.value,
        "data": {"quality_of_picture": 9, "brightness": 0.5},
        "llm_usage": {"tokens": 100},
    }

    response_images, failed_records, throttled_records = validate_img_quality(
        valid_attributes, processed_params, other_vals, submission_id
    )

    assert len(response_images) == 2
    assert not failed_records
    assert not throttled_records
    assert response_images[0]["checks_results"]["quality_of_picture"] == 9

@patch("core.utils.utils.remove_duplicate_images")
def test_validate_img_quality_invalid_images(
    mock_remove_duplicates,
    processed_params,
    other_vals,
    submission_id,
):
    attributes = {"images": "not_a_list"}
    response_images, failed_records, throttled_records = validate_img_quality(
        attributes, processed_params, other_vals, submission_id
    )

    assert response_images == []
    assert failed_records == []
    assert throttled_records == []


@patch("core.utils.utils.remove_duplicate_images")
@patch("core.utils.utils.get_images_to_process")
@patch("app.img_process.quality.quality_validation.img_validate_quality", side_effect=Exception("Mock processing error"))
def test_validate_img_quality_processing_exception(
    mock_img_validate_quality,
    mock_get_images_to_process,
    mock_remove_duplicates,
    valid_attributes,
    processed_params,
    other_vals,
    submission_id,
):
    mock_remove_duplicates.return_value = (valid_attributes["images"], [])
    mock_get_images_to_process.return_value = valid_attributes["images"]

    response_images, failed_records, throttled_records = validate_img_quality(
        valid_attributes, processed_params, other_vals, submission_id
    )

    assert not response_images
    assert len(failed_records) == 2  # Each image fails
    assert not throttled_records

@patch("core.utils.utils.remove_duplicate_images")
@patch("core.utils.utils.get_images_to_process")
@patch("app.img_process.quality.quality_validation.img_validate_quality")
def test_validate_img_quality_throttled_image(
    mock_img_validate_quality,
    mock_get_images_to_process,
    mock_remove_duplicates,
    valid_attributes,
    processed_params,
    other_vals,
    submission_id,
):
    mock_remove_duplicates.return_value = (valid_attributes["images"], [])
    mock_get_images_to_process.return_value = valid_attributes["images"]

    mock_img_validate_quality.return_value = {
        "status": "FAIL",
        "message": "malformed node or string: {'status': 'THROTTLE'}"
    }

    response_images, failed_records, throttled_records = validate_img_quality(
        valid_attributes, processed_params, other_vals, submission_id
    )

    assert not response_images

    # Assert each failed record has the expected throttle message (if that is expected)
    assert all(
        record["message"] == "malformed node or string: {'status': 'THROTTLE'}"
        for record in failed_records
    )

    # The number of failed records should match number of images
    assert len(failed_records) == 2

    # Assert no throttled records if they're not captured separately
    assert not throttled_records

