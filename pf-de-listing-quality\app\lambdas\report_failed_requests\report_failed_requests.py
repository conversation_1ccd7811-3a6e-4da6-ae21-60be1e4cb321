import json
import boto3
import os
import time
import logging
from datetime import datetime, timezone, timedelta

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Create boto3 clients for AWS services
sqs_client = boto3.client("sqs")
dynamodb_client = boto3.resource("dynamodb")
s3_client = boto3.client("s3")
ssm_client = boto3.client("ssm")

# Get environment variables for configuration
ENV = os.getenv("ENV")

# SSM parameter key names for retrieving configuration
S3_DIRECTORY = "failed-requests"
SSM_S3_BUCKET_KEY = f"/listing-quality/{ENV}/s3/tracking"
SSM_DYNAMODB_TABLE_KEY = f"/listing-quality/{ENV}/dynamodb/table"
SSM_DYNAMODB_TTL_KEY = f"/listing-quality/{ENV}/dynamodb/ttl"
SSM_NOTIFICATION_QUEUE_KEY = f"/listing-quality/{ENV}/queue/notification"

# Function to fetch SSM parameters from AWS Parameter Store
def fetch_ssm_parameters():
    parameter_names = [
        SSM_S3_BUCKET_KEY,
        SSM_DYNAMODB_TABLE_KEY,
        SSM_DYNAMODB_TTL_KEY,
        SSM_NOTIFICATION_QUEUE_KEY
    ]
   
    try:
        response = ssm_client.get_parameters(
            Names=parameter_names, WithDecryption=True
        )
        parameters = {}
        for param in response["Parameters"]:
            parameters[param["Name"]] = param["Value"]

        return parameters
    except Exception as e:
        logger.error(f"Error fetching SSM parameters: {str(e)}", exc_info=True)
        raise


# Fetch configuration parameters from SSM
params = fetch_ssm_parameters()

# Initialize configuration variables from SSM parameters
NOTIFICATION_SUCCESS_QUEUE_URL = params.get(SSM_NOTIFICATION_QUEUE_KEY)
S3_BUCKET_NAME = params.get(SSM_S3_BUCKET_KEY)
DYNAMODB_TABLE_NAME = params.get(SSM_DYNAMODB_TABLE_KEY)
DYNAMODB_RECORD_TTL = int(params.get(SSM_DYNAMODB_TTL_KEY)) if params.get(SSM_DYNAMODB_TTL_KEY) else 2

# Initialize DynamoDB table resource
DYNAMODB_TABLE = dynamodb_client.Table(DYNAMODB_TABLE_NAME) if DYNAMODB_TABLE_NAME else None


def lambda_handler(event, context):
    try:
        for record in event["Records"]:
            try:
                message_id = record["messageId"]

                # Parse the message body JSON and extract submission ID
                body = record["body"]
                body = json.loads(body)

                submission_id = body.get("submission_id")

                logger.info(
                    f"Processing DLQ Message Id: {message_id}, Request Id: {submission_id}, Body: {body}"
                )

                # Track the failed request in multiple persistence layers
                if submission_id:
                    # Store the raw failed request in S3 for debugging/auditing
                    write_json_to_s3(submission_id, body)
                    # Update or create entry in DynamoDB for tracking
                    log_failure_to_dynamodb(submission_id, body)
                # Send notification about the failure for downstream handling
                send_message_to_notification_queue(body)

            except Exception as e:
                logger.error(f"Exception processing DLQ message: {str(e)}", exc_info=True)
                continue

        return {
            "statusCode": 200,
            "body": json.dumps(
                {"message": "Processed all failed requests successfully"}
            ),
        }

    except Exception as e:
        logger.error(f"Error processing DLQ messages: {str(e)}", exc_info=True)
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


# Function to log failed request in DynamoDB - updates existing entry or creates new one
def log_failure_to_dynamodb(submission_id, body):
    try:
        # Check if item already exists in DynamoDB
        response = DYNAMODB_TABLE.get_item(Key={"RequestID": submission_id})
        if "Item" in response:
            # Update existing item to indicate failure
            DYNAMODB_TABLE.update_item(
                Key={"RequestID": submission_id},
                UpdateExpression="SET #st = :val",
                ExpressionAttributeNames={"#st": "status"},
                ExpressionAttributeValues={":val": "FAILED"},
            )
            logger.info("Updated status of Request Id: %s to FAILED", submission_id)
        else:
            # Create new item with failure status
            data = {
                "RequestID": submission_id,
                "api_type": body.get("type"),
                "output": json.dumps({}),
                "payload": json.dumps(body),
                "status": "FAILED",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "ttl": int(
                    (
                        datetime.now(timezone.utc)
                        + timedelta(hours=DYNAMODB_RECORD_TTL)
                    ).timestamp()
                ),
            }
            DYNAMODB_TABLE.put_item(Item=data)
            logger.info("Inserted failed Request Id: %s into DynamoDB", submission_id)

    except Exception as e:
        logger.error(
            f"Failed to log Request Id: {submission_id} to DynamoDB: {str(e)}",
            exc_info=True,
        )
        raise Exception(
            f"Failed to log Request Id: {submission_id} to DynamoDB: {str(e)}"
        )


# Function to store failed request JSON in S3 for debugging and audit purposes
def write_json_to_s3(submission_id, json_data):
    json_str = json.dumps(json_data)
    s3_path = f"{S3_DIRECTORY}/{submission_id}.json"

    try:
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=s3_path,
            Body=json_str,
            ContentType="application/json",
        )
        logger.info(
            f"Successfully uploaded Request Id: {submission_id}.json to S3://{S3_BUCKET_NAME}/{s3_path}"
        )
    except Exception as e:
        logger.error(
            f"Error uploading file to S3: {e}, Request Id: {submission_id}, data: {json_str}"
        )
        raise Exception(
            f"Error uploading file to S3: {e}, Request Id: {submission_id}, data: {json_str}"
        )


# Function to send failure notification to downstream systems via SQS
def send_message_to_notification_queue(body):
    if os.getenv("DISABLE_NOTIFICATIONS", "false").lower() == "true":
        logger.info("Notifications are disabled via DISABLE_NOTIFICATIONS env var. Skipping notification.")
        return
        
    try:
        sqs_client.send_message(
            QueueUrl=NOTIFICATION_SUCCESS_QUEUE_URL, MessageBody=json.dumps(body)
        )
        logger.info("Successfully added message in notification queue.")
    except Exception as e:
        logger.exception("Failed to add message in notification queue.")