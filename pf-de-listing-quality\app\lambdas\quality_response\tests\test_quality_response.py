import json
from unittest.mock import patch
import pytest
from quality_response import lambda_handler

@pytest.fixture
def ddb_item():
    # Simulate a DynamoDB item matching the new model
    return {
        "RequestID": "abc123",
        "status": "SUCCESS",
        "output": json.dumps({
            "listing_id": 123,
            "images": [
                {
                    "id": 1,
                    "src": "url1",
                    "checks_results": {
                        "rotation": False,
                        "quality_of_picture": 8,
                        "distortion": 1,
                        "is_it_boxed": True,
                        "people_on_photo": False,
                        "photo_classification": "living room",
                        "furnished": True,
                        "room_size": "medium",
                        "is_it_a_render": False,
                        "room_under_construction": False,
                        "room_category": "Modern",
                        "watermark": True,
                        "watermark_text": "dubbizzle",
                        "brightness": 4
                    }
                }
            ]
        })
    }

def test_lambda_handler_found(ddb_item):
    event = {"pathParameters": {"id": "abc123"}}
    context = {}
    with patch("quality_response.dynamodb_table") as mock_table:
        mock_table.get_item.return_value = {"Item": ddb_item}
        resp = lambda_handler(event, context)
        assert resp["statusCode"] == 200
        body = json.loads(resp["body"])
        assert body["status"] == 200
        assert body["message"] == "success"
        assert body["result_data"]["listing_id"] == 123
        assert isinstance(body["result_data"]["images"], list)
        assert body["result_data"]["images"][0]["id"] == 1
        assert body["result_data"]["images"][0]["checks_results"]["rotation"] is False

def test_lambda_handler_not_found():
    event = {"pathParameters": {"id": "notfound"}}
    context = {}
    with patch("quality_response.dynamodb_table") as mock_table:
        mock_table.get_item.return_value = {}
        resp = lambda_handler(event, context)
        assert resp["statusCode"] == 404
        body = json.loads(resp["body"])
        assert body["error"] == "Not found"

def test_lambda_handler_missing_id():
    event = {"pathParameters": {}}
    context = {}
    resp = lambda_handler(event, context)
    assert resp["statusCode"] == 400
    body = json.loads(resp["body"])
    assert body["error"] == "Missing required path parameter: id"

def test_lambda_handler_internal_error():
    event = {"pathParameters": {"id": "abc123"}}
    context = {}
    with patch("quality_response.dynamodb_table.get_item", side_effect=Exception("DDB error")):
        resp = lambda_handler(event, context)
        assert resp["statusCode"] == 500
        body = json.loads(resp["body"])
        assert body["error"] == "Internal server error"
