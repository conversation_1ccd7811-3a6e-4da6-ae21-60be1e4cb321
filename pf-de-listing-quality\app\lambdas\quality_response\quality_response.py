import os
import json
import boto3
import logging
from boto3.dynamodb.conditions import Key
from http import HTTPStatus
from decimal_encoder import DecimalEncoder

dynamodb = boto3.resource("dynamodb")
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Get environment and table name from environment variables or SSM
ENV = os.getenv("ENV", "staging")
DYNAMODB_TABLE_NAME = os.getenv("DYNAMODB_TABLE_NAME") or f"listing-quality-requests-tracking-{ENV}"
dynamodb_table = dynamodb.Table(DYNAMODB_TABLE_NAME)

def lambda_handler(event, context):
    """
    Lambda handler for GET /quality/response/{id}
    Fetches the output data for a given request ID from DynamoDB.
    """
    try:
        path_params = event.get("pathParameters") or {}
        request_id = path_params.get("id")
        if not request_id:
            logger.warning("Missing required path parameter: id")
            return api_json_response(HTTPStatus.BAD_REQUEST, {"error": "Missing required path parameter: id"})

        item = _get_item_from_dynamodb(request_id)
        if not item:
            logger.warning(f"No record found for id: {request_id}")
            return api_json_response(HTTPStatus.NOT_FOUND, {"error": "Not found", "message": f"No record found for id: {request_id}"})

        _parse_output_field(item)
        status_error = _validate_status(item, request_id)
        if status_error:
            return status_error

        return api_json_response(HTTPStatus.OK, build_response(item))

    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        return api_json_response(HTTPStatus.INTERNAL_SERVER_ERROR, {"error": "Internal server error", "message": str(e)})

def _get_item_from_dynamodb(request_id):
    try:
        resp = dynamodb_table.get_item(Key={"RequestID": request_id})
        return resp.get("Item")
    except Exception as e:
        logger.error(f"Error reading from DynamoDB for RequestID {request_id}: {e}")
        return None

def _parse_output_field(item):
    output = item.get("output")
    try:
        output = json.loads(output) if output else None
    except (TypeError, json.JSONDecodeError) as e:
        logger.error(f"Error parsing output field as JSON: {e}")
    item["output"] = output

def build_response(item):
    output = item["output"]
    result = {
        "listing_id": output.get("listing_id"),
        "images": []
    }
    for img in output.get("images", []):
        # Flatten checks_results
        img_result = {
            "id": img.get("id"),
            "src": img.get("src"),
            "checks_results": {
                "rotation": img["checks_results"].get("rotation"),
                "quality_of_picture": img["checks_results"].get("quality_of_picture"),
                "distortion": img["checks_results"].get("distortion"),
                "is_it_boxed": img["checks_results"].get("is_it_boxed"),
                "people_on_photo": img["checks_results"].get("people_on_photo"),
                "photo_classification": img["checks_results"].get("photo_classification"),
                "furnished": img["checks_results"].get("furnished"),
                "room_size": img["checks_results"].get("room_size"),
                "is_it_a_render": img["checks_results"].get("is_it_a_render"),
                "room_under_construction": img["checks_results"].get("room_under_construction"),
                "room_category": img["checks_results"].get("room_category"),
                "watermark": img["checks_results"].get("watermark"),
                "watermark_text": img["checks_results"].get("watermark_text"),
                "brightness": img["checks_results"].get("brightness"),
            }
        }
        result["images"].append(img_result)

    api_response = {
        "status": 200,
        "message": "success",
        "result_data": result
    }
    return api_response

def _validate_status(item, request_id):
    if item.get("status") != "SUCCESS":
        logger.warning(f"Request {request_id} has non-success status: {item.get('status')}")
        return api_json_response(
            HTTPStatus.UNPROCESSABLE_ENTITY,
            {
                "error": "Request did not succeed",
                "status": item.get("status"),
                "message": f"Request status is {item.get('status')}, not SUCCESS",
                "request_id": request_id
            }
        )
    return None

def api_json_response(status, body):
    return {
        "statusCode": status.value if hasattr(status, "value") else int(status),
        "headers": {"Content-Type": "application/json"},
        "body": json.dumps(body, cls=DecimalEncoder)
    }
