from decimal import Decimal
from json import JSONEncoder

import boto3
import os
import json
from typing import List, Tuple, Set, Optional
from app.img_process.quality.schema import ImageDetails
from config.config import config
from app.img_process.quality.schema import PhotoAttributes
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_warning,
    log_frame_error,
    log_frame_debug,
)
from datetime import datetime, timezone, timedelta

# Load configuration settings from config
config = config.config

# Initialize AWS clients for SQS and DynamoDB
sqs_client = boto3.client("sqs")
dynamodb_client = boto3.resource("dynamodb")
dynamodb_table_name = dynamodb_client.Table(config.get("dynamodb_table_name"))
dynamodb_record_ttl = config.get("dynamodb_record_ttl")

# Configuration for file paths and queue URLs
CONFIG_FILE = "config/utils/config_params.json"
REQUEST_PROCESSING_QUEUE_URL = config.get("request_processing_queue_url")
NOTIFICATION_SUCESS_QUEUE_URL = config.get("notification_sucess_queue_url")

# Initialize logger
logger = get_logger(__name__)


# Custom JSONEncoder to handle Decimal objects, converting them to float
class DecimalEncoder(JSONEncoder):
    def default(self, o):
        if isinstance(o, Decimal):
            return float(o)
        return super().default(o)


# Function to delete a message from the request processing queue after processing
def delete_message_from_request_processing_queue(submission_id, receipt_handle):
    """
    Deletes processed message from Request processing queue to prevent reprocessing.
    """
    log_frame_info(
        logger,
        message="Attempting to delete message from request processing queue for Request Id",
        submission_id=submission_id,
    )

    try:
        # If receipt handle is provided, delete the message from the queue
        if receipt_handle:
            sqs_client.delete_message(
                QueueUrl=REQUEST_PROCESSING_QUEUE_URL, ReceiptHandle=receipt_handle
            )
            log_frame_info(
                logger,
                message="Successfully deleted message from request processing queue for Request Id",
                submission_id=submission_id,
            )
        else:
            # Log a warning if no receipt handle is provided
            log_frame_warning(
                logger,
                message="No receipt_handle provided for Request Id, skipping deletion",
                submission_id=submission_id,
            )

    except Exception as e:
        # Log error and raise exception if deletion fails
        log_frame_error(
            logger,
            message="Failed to delete message from request processing queue for Request Id",
            submission_id=submission_id,
        )
        raise Exception(
            f"Failed to delete message from request processing queue {submission_id}: {str(e)}"
        )


# Function to add a message to the notification success queue after successful processing
def put_message_in_notification_success_queue(submission_id, response):
    if os.getenv("DISABLE_NOTIFICATIONS", "false").lower() == "true":
        log_frame_debug(
            logger,
            message="Notifications are disabled via DISABLE_NOTIFICATIONS env var. Skipping notification for Request Id",
            submission_id=submission_id,
        )
        return
        
    log_frame_debug(
        logger,
        message="Attempting to add message to notification success queue for Request Id",
        submission_id=submission_id,
    )
    try:
        # Send message to SQS notification success queue
        sqs_client.send_message(
            QueueUrl=NOTIFICATION_SUCESS_QUEUE_URL,
            MessageBody=json.dumps(response, cls=DecimalEncoder, ensure_ascii=False),
        )
        log_frame_info(
            logger,
            message="Successfully added message in notification queue for Request Id",
            submission_id=submission_id,
        )
    except Exception as e:
        # Log error if message addition fails
        log_frame_error(
            logger,
            message="Failed to add message in notification queue for Request Id",
            submission_id=submission_id,
        )
        raise Exception(
            f"Failed to add message in notification queue {submission_id}: {str(e)}"
        )


# Function to remove duplicate images based on 'src' attribute
def remove_duplicate_images(
    images: List[ImageDetails],
) -> Tuple[List[ImageDetails], List[ImageDetails]]:
    unique_images: List[ImageDetails] = []
    duplicates: List[dict] = []
    seen_src: Set[str] = set()  # Set to track unique 'src' values

    # Iterate through all images and classify them as unique or duplicates
    for image in images:
        if image.src in seen_src:
            # If the src is already seen, it's a duplicate
            duplicates.append(
                {
                    "id": image.id,
                    "message": f"Skipping image_id: {image.id} due to duplicate src_url.",
                    "error": "",
                }
            )
        else:
            # If not seen, add to the unique list and mark the src as seen
            seen_src.add(image.src)
            unique_images.append(image)

    return unique_images, duplicates


# Function to validate attributes of the photo (listing_id, country_code, images)
def attributes_validations(attributes: PhotoAttributes) -> Optional[str]:
    """
    Validates basic attributes and returns an error message if any check fails.
    Returns None if all validations pass.
    """
    try:
        # Check if the attributes object is valid
        if not isinstance(attributes, PhotoAttributes):
            log_frame_error(
                logger,
                message="Invalid attributes object. Expected PhotoAttributes but got",
                attributes=type(attributes),
            )
            return "Invalid attributes object."

        log_frame_info(
            logger,
            message="Starting basic validations for listing_id=, country_code=",
            listing_id=attributes.listing_id,
            country_code=attributes.country_code,
        )

        # Define validation rules as (attribute_value, condition_function, error_message)
        validations = [
            (
                attributes.listing_id,
                lambda x: isinstance(x, int) and x > 0,
                "Listing Id is not valid",
            ),
            (
                attributes.country_code,
                lambda x: isinstance(x, str) and bool(x.strip()),
                "Country Code is not valid",
            ),
            (
                attributes.images,
                lambda x: x and isinstance(x, list) and len(x) > 0,
                "Images list is not valid",
            ),
        ]

        # Iterate over validation rules and check conditions
        for field_value, condition, error_msg in validations:
            if not condition(field_value):
                log_frame_error(logger, message="Validation failed", error=error_msg)
                return error_msg  # Return the first validation error

        log_frame_info(logger, message="Basic validations passed successfully.")
        return None

    except Exception as e:
        # Log and return error message in case of unexpected validation error
        log_frame_error(
            logger, message="Unexpected error in basic validations", error=str(e)
        )
        return "Unexpected validation error."


# Function to get a listing record from DynamoDB based on submission_id
def get_listing_record_from_dynamodb(submission_id):
    try:
        response = dynamodb_table_name.get_item(Key={"RequestID": submission_id})
        return response.get("Item", None)  # Return item if found, else None
    except Exception as e:
        # Log error if fetching the record fails
        log_frame_error(
            logger, message="Error fetching record from DynamoDB", error=str(e)
        )
        return None


# Function to add or update a listing record in DynamoDB
def add_listing_record_to_dynamodb(submission_id, data):
    try:
        item = {"RequestID": submission_id, **data}
        response = dynamodb_table_name.put_item(Item=item)
        log_frame_info(
            logger,
            message="Successfully added or updated record in DynamoDB for Request Id",
        )
        return response
    except Exception as e:
        # Log error if adding or updating the record fails
        log_frame_error(
            logger, message="Error adding or updating record in DynamoDB:", error=str(e)
        )
        return None


# Function to filter out images that have already been processed and should be reprocessed
def filter_already_processed_images(dynamodb_record, images):
    images_to_reprocess = []
    throttled_image_ids = []
    processed_images = json.loads(dynamodb_record.get("output", "{}")).get("images", [])
    throttled_records = json.loads(dynamodb_record.get("output", "{}")).get(
        "throttled_records", []
    )

    # Extract throttled image ids
    for throttled_record in throttled_records:
        throttled_image_id = throttled_record.get("id")
        throttled_image_ids.append(throttled_image_id)

    # Filter out images that need to be reprocessed
    for image in images:
        if image.id in throttled_image_ids:
            images_to_reprocess.append(image)
    return processed_images, images_to_reprocess


# Function to get the current timestamp in ISO format
def get_current_timestamp():
    return datetime.now(timezone.utc).isoformat()


# Function to calculate TTL (Time to Live) timestamp based on configuration
def get_ttl_timestamp():
    ttl_hours = int(dynamodb_record_ttl)
    return int((datetime.now(timezone.utc) + timedelta(hours=ttl_hours)).timestamp())


# Function to get the images to process based on a configuration value
def get_images_to_process(images):
    """
    Truncates the list of images based on the configuration value.
    """
    images_batch = config.get("num_images_to_process")
    if not images_batch:
        return images  # Return all images if no limit is configured
    try:
        images_batch = int(images_batch)
        return images[:images_batch] if len(images) > images_batch else images
    except ValueError as e:
        # Log error if configuration value for batch size is invalid
        log_frame_error(
            logger,
            message="Invalid configuration value for 'images_batch' Using all images.",
            error=str(e),
            images_batch=images_batch,
        )
        return images


# Function to extract the value of a key from a JSON string
def get_json_value(json_string: str, key: str):
    """
    Extracts the value of a given key from a JSON string.

    :param json_string: The JSON string containing key-value pairs.
    :param key: The key whose value needs to be retrieved.
    :return: The value of the key if found, otherwise None.
    """
    try:
        json_data = json.loads(json_string)  # Convert JSON string to dict
        return json_data.get(key, None)  # Return value or None if key not found
    except json.JSONDecodeError:
        return {}


# Function to load parameters from a JSON file
def load_parameters():
    with open(CONFIG_FILE, "r") as file:
        return json.load(file)


# Extract input and output token costs from the configuration
input_token_cost_per_1k = get_json_value(
    config.get("bedrock_inference_config"), "input_token_cost"
)

output_token_cost_per_1k = get_json_value(
    config.get("bedrock_inference_config"), "output_token_cost"
)

# Convert the token costs to float
input_token_cost_per_1k = (
    float(input_token_cost_per_1k) if input_token_cost_per_1k else None
)
output_token_cost_per_1k = (
    float(output_token_cost_per_1k) if output_token_cost_per_1k else None
)


# Function to calculate LLM cost based on usage (input and output tokens)
def calculate_llm_cost(llm_usage_dict):
    """Calculate input/output token costs for a single LLM usage entry."""
    input_tokens = llm_usage_dict.get("inputTokens", 0)
    output_tokens = llm_usage_dict.get("outputTokens", 0)

    input_cost = (input_tokens / 1000) * input_token_cost_per_1k
    output_cost = (output_tokens / 1000) * output_token_cost_per_1k
    total_cost = input_cost + output_cost

    return {
        "inputCost": round(input_cost, 6),
        "outputCost": round(output_cost, 6),
        "totalCost": round(total_cost, 6),
    }


# Function to update LLM usage costs for a list of images
def update_llm_cost(images):
    """Update each image's LLM usage with cost calculations"""
    if input_token_cost_per_1k and output_token_cost_per_1k:
        for image in images:
            llm_usage = image.get("llm_usage", {})
            for usage_key, usage_data in llm_usage.items():
                if usage_data:  # Skip empty entries
                    llm_usage[usage_key]["cost"] = calculate_llm_cost(usage_data)


# Function to calculate and aggregate LLM usage metrics across multiple images
def calculate_llm_usage_metrics(images):
    """
    Calculate and aggregate LLM usage metrics across multiple images.
    """
    # Initialize totals
    metrics = {
        "inputTokens": 0,
        "outputTokens": 0,
        "inputTokenCost": 0.0,
        "outputTokenCost": 0.0,
        "totalCost": 0.0,
        "latencyMs": 0,
    }

    # Iterate through images and aggregate metrics
    for image in images:
        llm_usage = image.get("llm_usage", {})

        for usage_data in llm_usage.values():
            if not usage_data:  # Skip empty entries
                continue

            # Sum tokens
            input_tokens = usage_data.get("inputTokens", 0)
            output_tokens = usage_data.get("outputTokens", 0)

            metrics["inputTokens"] += input_tokens
            metrics["outputTokens"] += output_tokens
            if input_token_cost_per_1k and output_token_cost_per_1k:
                # Calculate costs
                metrics["inputTokenCost"] += (
                    input_tokens / 1000
                ) * input_token_cost_per_1k
                metrics["outputTokenCost"] += (
                    output_tokens / 1000
                ) * output_token_cost_per_1k

            # Sum latency (if available)
            metrics["latencyMs"] += usage_data.get("latencyMs", 0)

    # Calculate total cost
    metrics["totalCost"] = metrics["inputTokenCost"] + metrics["outputTokenCost"]

    # Round monetary values
    for cost_key in ["inputTokenCost", "outputTokenCost", "totalCost"]:
        metrics[cost_key] = round(metrics[cost_key], 4)

    return metrics


# Function to convert all floats in an object (dict/list) to Decimal type
def convert_floats_to_decimals(obj):
    if isinstance(obj, float):
        return Decimal(
            str(obj)
        )  # Convert float to Decimal (str prevents precision issues)
    elif isinstance(obj, dict):
        return {k: convert_floats_to_decimals(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [convert_floats_to_decimals(v) for v in obj]
    else:
        return obj
