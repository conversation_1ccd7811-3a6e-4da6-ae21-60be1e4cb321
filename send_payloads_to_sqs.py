import boto3
import json
from payload import li
import time

QUEUE_NAME = 'listing-quality-gen-ai-request-queue-staging'
REGION = 'ap-southeast-1'

# Get the SQS queue URL from the name
def get_queue_url(sqs_client, queue_name):
    response = sqs_client.get_queue_url(QueueName=queue_name)
    return response['QueueUrl']

def send_to_sqs_queue(sqs_client, queue_url, payload):
    try:
        message_body = json.dumps(payload)
        json.loads(message_body)  # Validate JSON
        response = sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=message_body
        )
        print(f"Sent payload for listing_id={payload.get('listing_id')} | MessageId={response.get('MessageId')}")
        return response
    except Exception as e:
        print(f"Error sending payload for listing_id={payload.get('listing_id')}: {e}")
        return None

def main():
    sqs_client = boto3.client('sqs', region_name=REGION)
    queue_url = get_queue_url(sqs_client, QUEUE_NAME)
    batch_size = 10
    total = len(li)
    for i in range(0, total, batch_size):
        batch = li[i:i+batch_size]
        print(f"Sending batch {i//batch_size + 1} ({len(batch)} payloads)...")
        for payload in batch:
            send_to_sqs_queue(sqs_client, queue_url, payload)
        if i + batch_size < total:
            print("Waiting 60 seconds before next batch...")
            time.sleep(120)

if __name__ == '__main__':
    main() 