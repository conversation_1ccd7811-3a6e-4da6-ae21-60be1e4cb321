import csv
import json
import random
import requests
import pandas as pd
from typing import List, Dict, Any
import os
from datetime import datetime

def create_request_payload(
    listing_id: int,
    country_code: str,
    image_urls: List[str],
    validate_params: List[str] = None
) -> Dict[str, Any]:
    """
    Create the request payload for the listing quality endpoint.
    
    Args:
        listing_id: The ID of the listing
        country_code: The country code (e.g., 'AE')
        image_urls: List of image URLs to validate
        validate_params: Optional list of validation parameters
        
    Returns:
        Dictionary containing the formatted request payload
    """
    # Default custom input list
    custom_input_list = {
        "photo_classification": [
            "Open kitchen", "Closed kitchen", "dining room", "bedroom", 
            "bathroom", "brochure", "floor plan", "map", "logo", 
            "living room", "garden", "balcony", "building exterior", 
            "parking", "MaidRoom", "laundry", "Corridor", "multiple_images"
        ],
        "room_category": ["Luxury", "Modern", "Classic"],
        "room_size": ["small", "medium", "large"]
    }
    
    # Create images list with IDs
    images = [{"id": i, "src": url} for i, url in enumerate(image_urls)]
    
    # Create the body payload
    body = {
        "listing_id": listing_id,
        "country_code": country_code,
        "validate_params": validate_params or [],
        "images": images,
        "custom_input_list": custom_input_list
    }
    
    # Create the full event payload
    event = {
        "resource": "/validate_quality",
        "path": "/validate_quality",
        "httpMethod": "POST",
        "requestContext": {},
        "multiValueQueryStringParameters": None,
        "body": json.dumps(body)
    }
    
    return event

def test_staging_endpoint(
    endpoint_url: str,
    listing_id: int,
    country_code: str,
    image_urls: List[str],
    validate_params: List[str] = None
) -> Dict[str, Any]:
    """
    Test the staging endpoint with the provided images.
    
    Args:
        endpoint_url: The URL of the staging endpoint
        listing_id: The ID of the listing
        country_code: The country code (e.g., 'AE')
        image_urls: List of image URLs to validate
        validate_params: Optional list of validation parameters
        
    Returns:
        The response from the endpoint
    """
    # Create the request payload
    payload = create_request_payload(
        listing_id=listing_id,
        country_code=country_code,
        image_urls=image_urls,
        validate_params=validate_params
    )
    
    # For direct API call, we need to extract the body content
    body_content = json.loads(payload["body"])
    
    # Make the request to the endpoint
    try:
        response = requests.post(endpoint_url, json=body_content, timeout=30)
        
        # Check if the request was successful
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return {"error": response.status_code, "message": response.text}
    except Exception as e:
        print(f"Exception occurred: {str(e)}")
        return {"error": "exception", "message": str(e)}

def process_images_in_batches(
    csv_file_path: str,
    endpoint_url: str,
    batch_size: int = 10,
    country_code: str = "AE"
) -> None:
    """
    Process images from CSV in batches, test them against the endpoint,
    and save results to a new CSV file.
    
    Args:
        csv_file_path: Path to the CSV file containing image URLs
        endpoint_url: URL of the staging endpoint
        batch_size: Number of images to process in each batch
        country_code: Country code for the requests
    """
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    
    # Extract image URLs from the PROPERTY_IMAGE_URL column
    image_urls = df['PROPERTY_IMAGE_URL'].tolist()
    
    # Create batches of image URLs
    batches = [image_urls[i:i + batch_size] for i in range(0, len(image_urls), batch_size)]
    
    # Prepare results
    results = []
    
    print(f"Processing {len(batches)} batches of images...")
    
    # Process each batch
    for i, batch in enumerate(batches):
        # Generate a random property ID for this batch
        property_id = random.randint(10000000, 99999999)
        
        print(f"Batch {i+1}/{len(batches)}: Processing {len(batch)} images with property ID {property_id}")
        
        # Filter out any None or empty URLs
        valid_urls = [url for url in batch if url and isinstance(url, str)]
        
        if valid_urls:
            # Test the batch against the endpoint
            response = test_staging_endpoint(
                endpoint_url=endpoint_url,
                listing_id=property_id,
                country_code=country_code,
                image_urls=valid_urls
            )
            
            # Store results for each image in the batch
            for j, url in enumerate(valid_urls):
                image_result = {
                    "batch_number": i+1,
                    "property_id": property_id,
                    "image_url": url,
                    "image_index": j,
                }
                
                # Add response data if available
                if isinstance(response, dict) and not response.get("error"):
                    # Extract relevant information from the response
                    try:
                        if "images" in response and j < len(response["images"]):
                            image_data = response["images"][j]
                            image_result.update({
                                "classification": json.dumps(image_data.get("classification", {})),
                                "quality_score": image_data.get("quality_score", "N/A"),
                                "is_valid": image_data.get("is_valid", False),
                                "validation_errors": json.dumps(image_data.get("validation_errors", []))
                            })
                        else:
                            image_result.update({
                                "classification": "{}",
                                "quality_score": "N/A",
                                "is_valid": False,
                                "validation_errors": "[]",
                                "error": "No image data in response"
                            })
                    except Exception as e:
                        image_result.update({
                            "error": f"Failed to parse response: {str(e)}",
                            "raw_response": json.dumps(response)[:100] + "..."  # Truncate long responses
                        })
                else:
                    image_result.update({
                        "error": response.get("message", "Unknown error"),
                        "error_code": response.get("error", "N/A")
                    })
                
                results.append(image_result)
        
        # Add a small delay between batches to avoid overwhelming the endpoint
        import time
        time.sleep(1)
    
    # Create a DataFrame from the results
    results_df = pd.DataFrame(results)
    
    # Generate output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(os.path.dirname(csv_file_path), f"results_{timestamp}.csv")
    
    # Save results to CSV
    results_df.to_csv(output_file, index=False)
    print(f"Results saved to {output_file}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Process images from CSV in batches and test against staging endpoint")
    parser.add_argument("--csv", required=True, help="Path to the CSV file containing image URLs")
    parser.add_argument("--endpoint", required=True, help="URL of the staging endpoint")
    parser.add_argument("--batch-size", type=int, default=10, help="Number of images to process in each batch")
    parser.add_argument("--country-code", default="AE", help="Country code for the requests")
    
    args = parser.parse_args()
    
    process_images_in_batches(
        csv_file_path=args.csv,
        endpoint_url=args.endpoint,
        batch_size=args.batch_size,
        country_code=args.country_code
    )
