import pytest
from core.exceptions.custom import CustomException  # Adjust import path if needed

def test_custom_exception_initialization():
    code = 400
    message = "Invalid input"

    exc = CustomException(code=code, message=message)

    assert exc.code == code
    assert exc.message == message
    assert exc.error_code == code

def test_custom_exception_defaults():
    exc = CustomException()

    assert exc.code is None
    assert exc.message is None
    assert exc.error_code is None
