import * as cdk from 'aws-cdk-lib';
import {Construct} from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as path from 'path';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as lambdaEventSources from 'aws-cdk-lib/aws-lambda-event-sources';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import {RetentionDays} from 'aws-cdk-lib/aws-logs';

export interface LambdaResourcesProps {
  stage: string;
  resourceNameSuffix: string;
  commonTags: { [key: string]: string };
  scope: Construct;
  accountId: string;
  region: string;
  imageTag?: string; // New property to accept the image tag
}

export class ListingQualityLambdaResources {
  public readonly requestQueuingLambda: lambda.Function;
  public readonly dispatcherLambda: lambda.Function;
  public readonly reportFailedRequestsLambda: lambda.Function;
  public readonly qualityResponseLambda: lambda.Function;
  public readonly requestInvocationLambda: lambda.Function;
  public readonly qualityListingLambda: lambda.Function;
  public readonly invalidRequestHandlerLambda: lambda.Function; // Added new Lambda property
  public readonly bedrockLogsGroup: logs.LogGroup;

  constructor(props: LambdaResourcesProps) {
    const { stage, resourceNameSuffix, commonTags, scope, accountId, region, imageTag } = props;
    const lambdaBasePath = path.join(__dirname, '../../../app/lambdas');

    // Import existing IAM roles
    const requestQueuingRole = iam.Role.fromRoleArn(
      scope,
      'RequestQueuingRole',
      `arn:aws:iam::${accountId}:role/listing-quality-request-queuing-lambda-role-${resourceNameSuffix}`,
      { mutable: false }
    );
    
    const dispatcherRole = iam.Role.fromRoleArn(
      scope,
      'DispatcherRole',
      `arn:aws:iam::${accountId}:role/listing-quality-dispatcher-request-queuing-lambda-${resourceNameSuffix}`,
      { mutable: false }
    );
    
    const reportFailedRequestsRole = iam.Role.fromRoleArn(
      scope,
      'ReportFailedRequestsRole',
      `arn:aws:iam::${accountId}:role/listing-quality-report-failed-requests-lambda-role-${resourceNameSuffix}`,
      { mutable: false }
    );
    
    const requestInvocationRole = iam.Role.fromRoleArn(
      scope,
      'RequestInvocationRole',
      `arn:aws:iam::${accountId}:role/listing-quality-request-invocation-lambda-role-${resourceNameSuffix}`,
      { mutable: false }
    );
    
    const qualityListingRole = iam.Role.fromRoleArn(
      scope,
      'QualityListingRole',
      `arn:aws:iam::${accountId}:role/listing-quality-quality-listing-lambda-role-${resourceNameSuffix}`,
      { mutable: false }
    );
    
    // Import the existing role for the new invalid request handler Lambda
    const invalidRequestHandlerRole = iam.Role.fromRoleArn(
      scope,
      'InvalidRequestHandlerRole',
      `arn:aws:iam::${accountId}:role/listing-quality-invalid-request-handler-lambda-role-${resourceNameSuffix}`,
      { mutable: false }
    );
    
    // Import the existing SQS queues
    const genAiRequestQueue = sqs.Queue.fromQueueArn(
      scope,
      'GenAiRequestQueue',
      `arn:aws:sqs:${region}:${accountId}:listing-quality-gen-ai-request-queue-${resourceNameSuffix}`
    );
    
    const requestProcessingQueueDlq = sqs.Queue.fromQueueArn(
      scope,
      'RequestProcessingQueueDlq',
      `arn:aws:sqs:${region}:${accountId}:listing-quality-request-processing-queue-${resourceNameSuffix}-dlq`
    );
    
    // Import the DLQ for Gen AI Request Queue for the new Lambda to process
    const genAiRequestQueueDlq = sqs.Queue.fromQueueArn(
      scope,
      'GenAiRequestQueueDlq',
      `arn:aws:sqs:${region}:${accountId}:listing-quality-gen-ai-request-queue-${resourceNameSuffix}-dlq`
    );
    
    // Environment-specific configuration for reporting
    const envConfig = {
      reportEnv: stage === 'production' ? 'production' : 'staging'
    };
    
    // Request Queuing Lambda
    this.requestQueuingLambda = new lambda.Function(scope, 'RequestQueuingLambda', {
      functionName: `listing-quality-request-queuing-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'request_queuing.lambda_handler',
      code: lambda.Code.fromAsset(path.join(lambdaBasePath, 'request_queuing')),
      memorySize: 128,
      timeout: cdk.Duration.seconds(50),
      environment: {
        ENV: envConfig.reportEnv, // Keep full environment name for internal logic 
        DIAMOND_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-diamond-sla-queue-${resourceNameSuffix}`,
        EMERALD_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-emerald-sla-queue-${resourceNameSuffix}`,
        RUBY_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-ruby-sla-queue-${resourceNameSuffix}`,
        SAPPHIRE_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-sapphire-sla-queue-${resourceNameSuffix}`,
        DISABLE_NOTIFICATIONS: 'false',
        LOG_LEVEL: 'INFO',
      },
      role: requestQueuingRole
    });
    
    // Add tags to the Lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.requestQueuingLambda).add(key, value);
    });
    
    // Add SQS trigger for Request Queuing Lambda
    this.requestQueuingLambda.addEventSource(
      new lambdaEventSources.SqsEventSource(genAiRequestQueue, {
        batchSize: 10,
        maxBatchingWindow: cdk.Duration.seconds(0)
      })
    );

    // Dispatcher Lambda
    this.dispatcherLambda = new lambda.Function(scope, 'DispatcherLambda', {
      functionName: `listing-quality-dispatcher-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_9, 
      handler: 'dispatcher_request_queuing.lambda_handler',
      code: lambda.Code.fromAsset(path.join(lambdaBasePath, 'dispatcher_request_queuing')),
      memorySize: 256,
      timeout: cdk.Duration.seconds(60),
      environment: {
        DIAMOND_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-diamond-sla-queue-${resourceNameSuffix}`,
        DIAMOND_RATIO: '0.4',
        EMERALD_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-emerald-sla-queue-${resourceNameSuffix}`,
        EMERALD_RATIO: '0.1',
        REQUEST_PROCESSING_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-request-processing-queue-${resourceNameSuffix}`,
        RUBY_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-ruby-sla-queue-${resourceNameSuffix}`,
        RUBY_RATIO: '0.3',
        SAPPHIRE_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-sapphire-sla-queue-${resourceNameSuffix}`,
        SAPPHIRE_RATIO: '0.2',
        TOTAL_PROCESSING_SIZE: '28',
        LISTING_QUALITY_INVOCATION_LAMBDA: `listing-quality-request-invocation-lambda-${resourceNameSuffix}`,
        LOG_LEVEL: 'INFO',
      },
      role: dispatcherRole
    });
    
    // Add tags to the Lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.dispatcherLambda).add(key, value);
    });

    // Report Failed Requests Lambda
    this.reportFailedRequestsLambda = new lambda.Function(scope, 'ReportFailedRequestsLambda', {
      functionName: `listing-quality-report-failed-requests-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'report_failed_requests.lambda_handler',
      code: lambda.Code.fromAsset(path.join(lambdaBasePath, 'report_failed_requests')),
      memorySize: 128,
      timeout: cdk.Duration.seconds(50),
      environment: {
        ENV: envConfig.reportEnv, // Keep full environment name for internal logic
        DISABLE_NOTIFICATIONS: 'false',
        LOG_LEVEL: 'INFO',
      },
      role: reportFailedRequestsRole
    });
    
    // Add tags to the Lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.reportFailedRequestsLambda).add(key, value);
    });
    
    // Add SQS trigger for Report Failed Requests Lambda
    this.reportFailedRequestsLambda.addEventSource(
      new lambdaEventSources.SqsEventSource(requestProcessingQueueDlq, {
        batchSize: 10,
        maxBatchingWindow: cdk.Duration.seconds(0)
      })
    );

    // NEW Lambda: Invalid Request Handler Lambda
    this.invalidRequestHandlerLambda = new lambda.Function(scope, 'InvalidRequestHandlerLambda', {
      functionName: `listing-quality-invalid-request-handler-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'invalid_request_handler.lambda_handler',
      code: lambda.Code.fromAsset(path.join(lambdaBasePath, 'invalid_request_handler')),
      memorySize: 128,
      timeout: cdk.Duration.seconds(50),
      environment: {
        ENV: envConfig.reportEnv,
        DISABLE_NOTIFICATIONS: 'false',
        LOG_LEVEL: 'INFO',
      },
      role: invalidRequestHandlerRole
    });
    
    // Add tags to the new Lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.invalidRequestHandlerLambda).add(key, value);
    });
    
    // Add SQS trigger for Invalid Request Handler Lambda from gen-ai-request-queue DLQ
    this.invalidRequestHandlerLambda.addEventSource(
      new lambdaEventSources.SqsEventSource(genAiRequestQueueDlq, {
        batchSize: 10,
        maxBatchingWindow: cdk.Duration.seconds(0)
      })
    );

    // Fetch Quality Response Lambda
    this.qualityResponseLambda = new lambda.Function(scope, 'QualityResponseLambda', {
      functionName: `listing-quality-response-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'quality_response.lambda_handler',
      code: lambda.Code.fromAsset(path.join(lambdaBasePath, 'quality_response')),
      memorySize: 128,
      timeout: cdk.Duration.seconds(300),
      environment: {
        ENV: envConfig.reportEnv,
        DYNAMODB_TABLE_NAME: `listing-quality-requests-tracking-${resourceNameSuffix}`,
        LOG_LEVEL: 'INFO',
      },
      role: qualityListingRole,
      logRetention: RetentionDays.THREE_MONTHS,
    });

    // Add tags to lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.qualityResponseLambda).add(key, value);
    });

    // Request Invocation Lambda
    this.requestInvocationLambda = new lambda.Function(scope, 'RequestInvocationLambda', {
      functionName: `listing-quality-request-invocation-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'request_invocation.lambda_handler',
      code: lambda.Code.fromAsset(path.join(lambdaBasePath, 'request_invocation')),
      memorySize: 128,
      timeout: cdk.Duration.seconds(60),
      environment: {
        LISTING_QUALITY_API_LAMBDA: `listing-quality-quality-listing-lambda-${resourceNameSuffix}`,
        REQUEST_PROCESSING_QUEUE_URL: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality-request-processing-queue-${resourceNameSuffix}`,
        LOG_LEVEL: 'INFO',
      },
      role: requestInvocationRole
    });
    
    // Add tags to the Lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.requestInvocationLambda).add(key, value);
    });

    // Import ECR repository
    const ecrRepository = ecr.Repository.fromRepositoryArn(
      scope,
      'ListingQualityEcrRepo',
      `arn:aws:ecr:${region}:${accountId}:repository/pf-de-listing-quality`
    );

    // Quality Listing Lambda
    this.qualityListingLambda = new lambda.Function(scope, 'QualityListingLambda', {
      functionName: `listing-quality-quality-listing-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.FROM_IMAGE,
      code: lambda.Code.fromEcrImage(ecrRepository, {
        tagOrDigest: imageTag || 'latest'
      }),
      handler: lambda.Handler.FROM_IMAGE,
      memorySize: 3540,
      ephemeralStorageSize: cdk.Size.mebibytes(1024),  // Increased from default 512MB to 1024MB
      timeout: cdk.Duration.minutes(3),
      environment: {
        ENV: stage === 'staging' ? 'staging' : 'prod',
        CONCURRENT_WORKERS: '15',
        DEFAULT_REGION_VALUE: region,
        DISABLE_NOTIFICATIONS: 'false',
        LOG_LEVEL: 'INFO',
        MOCK_LLM_RESPONSE: 'False',
        USE_BEDROCK_INVOKE: 'False'
      },
      role: qualityListingRole
    });
    
    // Add tags to the Lambda function
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.qualityListingLambda).add(key, value);
    });

    // Log retention is longer for production
    const logRetention = stage === 'production' 
      ? logs.RetentionDays.ONE_MONTH 
      : logs.RetentionDays.TWO_WEEKS;

    // Create CloudWatch Log Group for Bedrock invocations
    this.bedrockLogsGroup = new logs.LogGroup(scope, 'BedrockInvocationsLogs', {
      logGroupName: `bedrock-model-invocation-logs-${resourceNameSuffix}`,
      retention: logRetention,
      removalPolicy: stage === 'production' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    // Apply the same common tags to the log group
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this.bedrockLogsGroup).add(key, value);
    });
  }
}
