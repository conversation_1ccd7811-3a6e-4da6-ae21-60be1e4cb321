import ast
import concurrent.futures
import contextvars
import os
import random
from typing import Dict, List

from dotenv import find_dotenv, load_dotenv

from app.img_process.quality.process import ImageQualityAnalysisModel
from app.img_process.quality.schema import PhotoAttributes
from core.enums.status import ResponseStatus
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_debug,
    log_frame_warning,
    log_frame_error,
)
from core.utils.utils import get_images_to_process, remove_duplicate_images

# Define a context variable for trace_id
trace_id_var = contextvars.ContextVar("trace_id", default="N/A")

# Initialize a logger specific to this module
logger = get_logger(__name__)

# env variable
load_dotenv(find_dotenv())

# get MAX_WORKER from enviorment
MAX_WORKERS = int(os.getenv("CONCURRENT_WORKERS", "5"))


def img_validate_quality(
    attributes: PhotoAttributes, images: dict, processed_params: dict, other_vals: dict
):
    """Validates image quality using ImageQualityAnalysisModel with error handling."""

    try:
        # Log the start of the image quality validation process
        log_frame_info(
            logger,
            message="Starting image quality validation for attributes:",
            attributes=attributes,
        )

        # Validate if the 'images' input is properly formed and contains a 'src' attribute
        if not images or not hasattr(images, "src") or not images.src:
            log_frame_error(
                logger,
                message="Invalid images object. Missing 'src' attribute.",
            )
            # Return failure response if validation fails
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Invalid image source.",
                "data": None,
            }

        # Initialize the image quality analysis model
        model = ImageQualityAnalysisModel()

        # Log debug info before processing images
        log_frame_debug(
            logger,
            message="Calling process_images method with image source: ",
            images_src=images.src,
        )

        # Try to process the image and catch any exceptions during the process
        try:
            checks_results = model.process_images(
                images.src, attributes, processed_params, other_vals
            )
        except Exception as e:
            # Log processing error and return failure response
            log_frame_error(
                logger,
                message="Error during image processing.",
                error=str(e),
                exc_info=True,
            )

            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Image processing error: {str(e)}",
                "data": None,
            }

        # If the result status indicates failure or throttling, log and return the error
        if checks_results.get("status") in [
            ResponseStatus.FAIL.value,
            ResponseStatus.THROTTLE.value,
        ]:
            log_frame_error(
                logger,
                message="Image quality validation failed.",
                error=str(checks_results),
            )

            return checks_results

        # Filter out only the relevant results from the response data
        data = checks_results.get("data", {})
        ret_results = {
            key: data[key]
            for key in data
            if key in processed_params or key == "quality_of_details"
        }

        # Replace original data with filtered results
        checks_results["data"] = ret_results

        # Log success and the filtered results
        log_frame_info(
            logger,
            message="Image quality validation successful. Processed results:",
            ret_results=ret_results,
        )

        # Return the processed and filtered results
        return checks_results

    except Exception as e:
        # Catch any unexpected errors during the entire validation process
        log_frame_error(
            logger,
            message="Unexpected error in image validation.",
            error=str(e),
            exc_info=True,
        )

        # Return a generic failure response for any unexpected error
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"Unexpected error: {str(e)}",
            "data": None,
        }


def process_parameters(parameters: List[Dict]) -> Dict:
    result = {}
    sample_output = {}

    # Iterate over each parameter dictionary in the 'parameters' list
    for param in parameters:
        param_name = param.get("param_name")  # Extract from dictionary
        param_prompt = param.get("param_prompt")  # Extract from dictionary
        param_type = param.get("param_type")  # Extract from dictionary

        # If both parameter name and prompt are available, add them to the result dictionary
        if param_name and param_prompt:
            result[param_name] = param_prompt

        # Depending on the type of the parameter, generate a sample output and add it to 'sample_output'
        if param_type == "Bool":
            sample_output[param_name] = "True|False"
        elif param_type == "int":
            sample_output[param_name] = random.randint(1, 10)
        elif param_type == "str":
            sample_output[param_name] = "example string"

    result["sample_output"] = sample_output
    return result


def convert_value(value):
    """Converts string representations of boolean values to actual booleans."""

    try:
        # Check if the value is None, and log a warning if so
        if value is None:
            log_frame_warning(
                logger,
                message="Received None value, returning None.",
            )

            return None

        # If the value is a string, process it
        if isinstance(value, str):
            lower_value = value.strip().lower()  # Normalize for case insensitivity

            # Check if the string is 'true', and convert it to a boolean True
            if lower_value == "true":
                log_frame_info(
                    logger, message="Converting string 'True' to boolean True"
                )
                return True
            
            # Check if the string is 'false', and convert it to a boolean False
            elif lower_value == "false":
                log_frame_info(
                    logger, message="Converting string 'False' to boolean False"
                )
                return False

        # If the value is not a string, return it unchanged
        return value

    except Exception as e:
        # Log error if an exception occurs during conversion
        log_frame_error(
            logger, message="Error converting value.", error=str(e), exc_info=True
        )
        return value  # Return original value on error


def process_checks_results(checks_results):
    """Processes and converts values in checks_results with error handling."""

    try:
        # Check if 'checks_results' is a dictionary, log a warning if it's not
        if not isinstance(checks_results, dict):
            log_frame_warning(
                logger,
                message="Invalid input: Expected a dictionary but got ",
                checks_results_type=type(checks_results),
            )

            return {}

        # Log debug information about the checks_results being processed
        log_frame_debug(
            logger,
            message="Processing checks_results",
            checks_results=checks_results,
        )

        # Create an empty dictionary to store processed results
        processed_results = {}

        # Iterate over the key-value pairs in checks_results to process each value
        for key, value in checks_results.items():
            try:
                # Convert the value using the 'convert_value' function and store the result
                processed_results[key] = convert_value(value)
            except Exception as e:
                # If an error occurs during conversion, log the error and preserve the original value
                log_frame_error(
                    logger,
                    message="Error converting key:  | Value:  | Error: ",
                    key=key,
                    value=value,
                    error=str(e),
                    exc_info=True,
                )

                processed_results[key] = value  # Preserve original value on failure

        # Log the final processed results
        log_frame_info(
            logger,
            message="Final processed checks_results",
            processed_results=processed_results,
        )
        return processed_results

    except Exception as e:
        # If any unexpected error occurs in the processing, log it and return an empty dictionary
        log_frame_error(
            logger,
            message="Unexpected error in processing checks_results: ",
            error=str(e),
            exc_info=True,
        )

        return {} # Return an empty dictionary if an error occurs at the top level


def convert_str_to_bool(data):
    """Converts string values to booleans in 'checks_results' field of a list of items, with error handling."""

    try:
        # Check if 'data' is a list; log a warning and return an empty list if it's not
        if not isinstance(data, list):
            log_frame_warning(
                logger,
                message="Invalid input: Expected a list but got ",
                data_type=type(data),
            )

            return []

        # Log debug information about the received data for conversion
        log_frame_debug(
            logger,
            message="Received data for conversion: ",
            data=data,
        )

        # Iterate over each item in the list to process it
        for item in data:
            try:
                # If the item is not a dictionary, log a warning and skip it
                if not isinstance(item, dict):
                    log_frame_warning(
                        logger,
                        message="Skipping invalid item: Expected dict but got ",
                        item_type=type(item),
                    )

                    continue
                
                # If 'checks_results' is a key in the item, process its value
                if "checks_results" in item:
                    log_frame_debug(
                        logger,
                        message="Processing checks_results for item: ",
                        item=item,
                    )

                    # Process the 'checks_results' field using the 'process_checks_results' function
                    item["checks_results"] = process_checks_results(
                        item.get("checks_results", {})
                    )

            except Exception as e:
                # If an error occurs during the processing of an item, log the error
                log_frame_error(
                    logger,
                    message="Error processing item:  | Error: ",
                    item=item,
                    error=str(e),
                    exc_info=True,
                )

        # Log the final converted data after processing all items
        log_frame_info(logger, message="Final converted data: ", data=data)
        return data # Return the modified data list after processing

    except Exception as e:
        # If any unexpected error occurs in the processing, log it and return an empty dictionary
        log_frame_error(
            logger,
            message="Unexpected error in convert_str_to_bool: ",
            error=str(e),
            exc_info=True,
        )

        return []


def validate_img_quality(
    attributes: PhotoAttributes,
    processed_params: dict,
    other_vals: dict,
    submission_id: str,
):
    """Validates image quality by processing images in parallel with error handling."""

    try:
        # Set the trace_id for logging, which helps track the process
        trace_id_var.set(submission_id)

        # Initialize lists to store processed images, failed records, and throttled records
        response_images, failed_records, throttled_records = [], [], []

        log_frame_info(logger, message="Starting image quality validation...")

        # Validate that the 'images' field in attributes is a list
        images = attributes.get("images", [])
        if not isinstance(images, list):

            # Log a warning if images is not a list
            log_frame_warning(
                logger,
                message="Invalid image list in attributes. Expected a list but got ",
                images_type=type(images),
            )

            return [], [], []
        
        # Log the received attributes for debugging
        log_frame_debug(
            logger,
            message="Received attributes: ",
            attributes=attributes,
        )

        # Log the processed parameters for debugging
        log_frame_debug(
            logger,
            message="Processing parameters",
            processed_params=processed_params,
        )

        # Remove duplicate images and prepare them for processing
        try:
            unique_images, duplicates = remove_duplicate_images(images)
            unique_images = get_images_to_process(unique_images)
            attributes["images"] = unique_images
            failed_records.extend(duplicates)
            log_frame_info(
                logger,
                message="Removed duplicate images. unique images remaining.",
                duplicates=len(duplicates),
                unique_images=len(unique_images),
            )

        except Exception as e:
            # Log an error and return empty lists if there is an issue removing duplicates
            log_frame_error(
                logger, message="Error removing duplicate images.", error=str(e)
            )
            return [], [], []

        # Check if no valid images remain after removing duplicates
        if not unique_images:
            log_frame_warning(
                logger,
                message="No valid images left after removing duplicates. Returning empty results.",
            )

            return response_images, failed_records, throttled_records

        # Process images in parallel using ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_image = {}

            # Submit image processing tasks to the executor for each unique image
            for image in unique_images:
                # Capture the current execution context to propagate it to threads
                ctx = contextvars.copy_context()
                future = executor.submit(
                    ctx.run,
                    img_validate_quality,
                    attributes.copy(),
                    image,
                    processed_params,
                    other_vals,
                )
                future_to_image[future] = image

            # Process the results as they complete
            for future in concurrent.futures.as_completed(future_to_image):
                image = future_to_image[future]
                try:
                    result = future.result()
                    if not result:
                        failed_records.append(
                            {
                                "image_id": image.id,
                                "message": f"Skipping image_id: {image.id} due to processing error.",
                            }
                        )
                        log_frame_error(
                            logger,
                            message="Skipping image - trace_id due to processing error.",
                            image_id=image.id,
                            submission_id=submission_id,
                            trace_id=trace_id_var.get(),
                        )

                        continue
                    
                    # If the image was processed successfully, add to response images
                    if result.get("status") == ResponseStatus.SUCCESS.value:
                        response_images.append(
                            {
                                "id": image.id,
                                "src": str(image.src),
                                "checks_results": result.get("data", {}),
                                "llm_usage": result.get("llm_usage", {}),
                            }
                        )
                        log_frame_info(
                            logger,
                            message="Successfully processed image - trace_id",
                            image_id=image.id,
                            submission_id=submission_id,
                            trace_id=trace_id_var.get(),
                        )

                    elif (
                        result.get("status")
                        == ResponseStatus.THROTTLE.value
                    ):
                        # If the image is throttled, add it to throttled records
                        throttle_image = {
                            "id": image.id,
                            "src": str(image.src),
                            "message": result["message"],
                            "checks_results": result.get("data", {}),
                            "llm_usage": result.get("llm_usage", {}),
                        }
                        throttled_records.append(throttle_image)
                        log_frame_error(
                            logger,
                            message="Throttled image  - trace_id=",
                            image_id=image.id,
                            submission_id=submission_id,
                            trace_id=trace_id_var.get(),
                        )
                    else:
                        # If there is an unknown status, add to failed records
                        failed_records.append(
                            {
                                "image_id": image.id,
                                "message": result.get("message", "Unknown error"),
                            }
                        )
                        log_frame_warning(
                            logger,
                            message="Image  processing failed - trace_id=: ",
                            image_id=image.id,
                            submission_id=submission_id,
                            trace_id=trace_id_var.get(),
                            error=result.get("message", "Unknown error"),
                        )

                except Exception as e:
                    # Handle exceptions during image processing
                    error_message = f"Error processing image {image.id} - trace_id={trace_id_var.get()}: {str(e)}"
                    failed_records.append(
                        {"image_id": image.id, "message": error_message}
                    )

                    log_frame_error(
                        logger,
                        message=error_message,
                        image_id=image.id,
                        submission_id=submission_id,
                        trace_id=trace_id_var.get(),
                    )

        # Convert string values in the response to booleans in 'checks_results'
        response_images = convert_str_to_bool(response_images)

        # Log the final results of the validation process
        log_frame_info(
            logger,
            message="Completed image quality validation. Processed: %d, Failed: %d, Throttled: %d",
            response_images=len(response_images),
            failed_records=len(failed_records),
            throttled_records=len(throttled_records),
        )

        return response_images, failed_records, throttled_records

    except Exception as e:
        # Log an error if any unexpected exception occurs during the process
        log_frame_error(
            logger,
            message="Unexpected error in validate_img_quality",
            error=str(e),
            exc_info=True,
        )
        return [], [], []
