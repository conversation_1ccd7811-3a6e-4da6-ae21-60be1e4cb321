import boto3

from config.config import config
from core.services.config_loader import PreConfigParameters
from core.logger.logger import get_logger

# Load configuration from a central config object
config = config.config

# Load pre-configured secrets related to image quality validation
pre_configs = PreConfigParameters().get_quality_validation_secrets()

# Initialize an S3 client for interacting with AWS S3
s3_client = boto3.client("s3")

# Initialize a logger for logging information, warnings, and errors
logger = get_logger(__name__)


class ValidationAttributeDetails:
    """Image quality validation attributes"""

    # Load pre-config values if they exist, otherwise use empty dictionaries
    if pre_configs:
        
        # Load photo classification settings (e.g., Bathroom, Bedroom, etc.)
        pre_config_photo_classification = pre_configs.get(
            "pre_config_photo_classification", {}
        )

        # Load room category settings (e.g., Modern, Classic, etc.)
        pre_config_room_category = pre_configs.get("pre_config_room_category", {})
        # Load room size settings (e.g., Small, Medium, Large)
        pre_config_room_size = pre_configs.get("pre_config_room_size", {})
    else:
        # Log error if secrets/configs failed to load
        logger.error("pre_configs is None. Ensure config.yaml is properly loaded.")
        # Fallback to empty configs to avoid breaking the app
        pre_config_photo_classification = {}
        pre_config_room_category = {}
        pre_config_room_size = {}

    # Placeholder for image to be validated — can be set later
    image = None

    # A sample output dictionary representing various image validation results
    sample_output = {
        "rotation": True | False,                    # Whether the image is rotated correctly
        "quality_of_picture": 5,                     # Overall quality score (e.g., out of 10)
        "brightness": 5,                             # Brightness score
        "distortion": 6,                             # Distortion score
        "is_it_boxed": True | False,                 # Whether the image has black/white borders
        "people_on_photo": True | False,             # Whether people are detected in the image
        "watermark": True | False,                   # Whether a watermark is detected
        "watermark_text": "Some watermark text",     # Text of the detected watermark (if any)
        "photo_classification": "Bathroom",          # Type of room classified by model
        "furnished": True | False,                   # Whether the room is furnished
        "room_size": "large",                        # Room size classification
        "is_it_a_render": True | False,              # Whether the image is a digital render
        "room_under_construction": True | False,     # Whether the room is under construction
        "room_category": "Modern",                   # Category of room (style, design, etc.)
    }
