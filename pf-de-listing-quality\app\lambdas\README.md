## Lambda functions
This directory contains the source code for all lambda functions.

### Run Tests
### PyTest Testing Flow

1. **Create and Active a Virtual Environment **
   ```bash
      python -m venv venv
      source venv/bin/activate
   ```
2. **Install Dependencies**
   ```bash
      pip install boto3 pytest pytest-cov
   ```

3. **Now navigate to each lambda function directory and run the Tests with coverage**
   ```bash
      coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v . && coverage report -m  -i
   ```
