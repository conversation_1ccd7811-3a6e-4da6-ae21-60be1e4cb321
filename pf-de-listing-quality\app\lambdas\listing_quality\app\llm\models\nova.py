"""
Module for interacting with AWS Bedrock LLM models using the boto3 SDK and the Converse API.
"""

import json
import ast
import time
import io
import os
import base64

import boto3
from botocore.exceptions import ClientError
from config.config import config
from core.enums.status import ResponseStatus
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_debug, log_frame_warning
from core.utils.utils import get_json_value

# Initialize logger for this module
logger = get_logger(__name__)

# Load configuration object from the config module
config_obj = config.config
config_obj_safe = config_obj or {}

# Global configuration and client initialization
# Get region from config or environment
bedrock_region = config_obj_safe.get("bedrock_model_region") or os.getenv('AWS_REGION') or 'us-east-1'

bedrock_client = None
try:
    bedrock_client = boto3.client(
        service_name='bedrock-runtime',
        region_name=bedrock_region
    )
    log_frame_info(logger, message="Nova Bedrock client initialized successfully")
except Exception as e:
    log_frame_error(logger, message=f"Failed to initialize Nova Bedrock client: {str(e)}")
    bedrock_client = None

# Retry configuration from config or default
bedrock_max_attempts = int(
    get_json_value(config_obj_safe.get("nova_inference_config"), "max_attempts") or
    get_json_value(config_obj_safe.get("bedrock_inference_config"), "max_attempts") or 3
)

class BedrockLlm:
    """
    A class for interacting with AWS Bedrock LLM models using the new Converse API.
    """

    @classmethod
    def converse_image_text(cls, image_bytes, prompt):
        """
        Image+Text inference using the Bedrock Converse API.
        Returns { status, result, llm_usage } where result is a parsed object when possible.
        """
        if not bedrock_client:
            return {"status": ResponseStatus.FAIL.value, "message": "Bedrock client not initialized"}

        start_time = time.time()

        # Get inference configuration from config (JSON string from SSM)
        nova_config_json = config_obj_safe.get("nova_inference_config", "")

        # Use the correct Amazon Nova Pro model ID (configurable)
        model_id = get_json_value(nova_config_json, "nova_model_name") or "amazon.nova-pro-v1:0"

        # The Bedrock Converse API requires image bytes to be base64 encoded
        base64_image = base64.b64encode(image_bytes).decode('utf-8')

        # Get image format from config or default to jpeg
        image_format = get_json_value(nova_config_json, "image_format") or "jpeg"

        # Construct the messages array for the Bedrock Converse API
        messages = [{
            "role": "user",
            "content": [
                {
                    "image": {
                        "format": image_format,
                        "source": {
                            "bytes": base64_image
                        }
                    }
                },
                {
                    "text": prompt
                }
            ]
        }]

        # Bedrock Converse API requires tools to be an array of tool objects
        tools = [
            {
                "toolSpec": {
                    "name": "analyze_image_quality_comprehensive",
                    "description": "Comprehensive analysis of real-estate listing photos including quality metrics, watermark detection, and competitor identification",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "rotation": {"type": "boolean", "description": "Whether the image is rotated"},
                                "quality_of_picture": {"type": "integer", "minimum": 0, "maximum": 10, "description": "Overall picture quality score"},
                                "distortion": {"type": "integer", "minimum": 0, "maximum": 10, "description": "Level of image distortion"},
                                "is_it_boxed": {"type": "boolean", "description": "Whether the image has a border/box"},
                                "people_on_photo": {"type": "boolean", "description": "Whether people are visible in the image"},
                                "photo_classification": {"type": "string", "description": "Type of room/area shown in the image"},
                                "furnished": {"type": "boolean", "description": "Whether the space appears furnished"},
                                "room_size": {"type": "string", "enum": ["small", "medium", "large", "unidentified room size", "not available"], "description": "Size of the room/space"},
                                "is_it_a_render": {"type": "boolean", "description": "Whether the image is a 3D render"},
                                "room_under_construction": {"type": "boolean", "description": "Whether the space is under construction"},
                                "room_category": {"type": "string", "description": "Style category of the room"},
                                "text": {"type": "boolean", "description": "Whether any text or watermark text is present in the image"},
                                "watermark": {"type": "boolean", "description": "Whether any watermark is present in the image"},
                                "watermark_text": {"type": "string", "description": "The exact text content of any watermark found in the image"},
                                "is_competitor_watermark": {"type": "boolean", "description": "Whether the detected watermark text matches any of these competitors: bayut, dubbizzle, aqar, aqarmap, elbayt, re/max, wasalt, skyloov"},
                                "competitor_name": {"type": "string", "description": "The specific competitor name if a competitor watermark is detected, empty string otherwise"}
                            },
                            "required": [
                                "rotation", "quality_of_picture", "distortion", "is_it_boxed",
                                "people_on_photo", "photo_classification", "furnished", "room_size",
                                "is_it_a_render", "room_under_construction", "room_category",
                                "text", "watermark", "watermark_text", "is_competitor_watermark",
                                "competitor_name"
                            ]
                        }
                    }
                }
            }
        ]

        # Bedrock inference configuration from config
        bedrock_inference_config = {
            "maxOutputTokens": int(get_json_value(nova_config_json, "nova_max_tokens") or 4096),
            "temperature": float(get_json_value(nova_config_json, "nova_temperature") or 0.0),
            "topP": float(get_json_value(nova_config_json, "nova_top_p") or 0.95)
        }

        # The Bedrock Converse API payload
        body = {
            "messages": messages,
            "toolConfig": {
                "tools": tools,
                "toolChoice": {
                    "tool": {
                        "name": "analyze_image_quality_comprehensive"
                    }
                }
            },
            "inferenceConfig": bedrock_inference_config
        }

        # LOG: Image+Text model call details
        log_frame_info(logger, f"🖼️ NOVA BEDROCK CONVERSE IMAGE+TEXT CALL STARTED")
        log_frame_info(logger, f"📋 Model: {model_id}")
        log_frame_info(logger, f"🔧 Config: temp={bedrock_inference_config['temperature']}, max_tokens={bedrock_inference_config['maxOutputTokens']}, top_p={bedrock_inference_config['topP']}")
        log_frame_info(logger, f"🖼️ Image size: {len(image_bytes)} bytes")
        log_frame_info(logger, f"💬 Prompt: {prompt}")

        response = None
        for attempt in range(bedrock_max_attempts):
            try:
                log_frame_debug(logger, f"🔄 Attempt {attempt + 1}/{bedrock_max_attempts}")
                response = bedrock_client.converse(
                    modelId=model_id,
                    messages=body["messages"],
                    toolConfig=body["toolConfig"],
                    inferenceConfig=body["inferenceConfig"]
                )
                log_frame_info(logger, f"✅ Bedrock Converse request successful on attempt {attempt + 1}")
                break
            except ClientError as e:
                if e.response['Error']['Code'] == 'ThrottlingException':
                    log_frame_warning(logger, f"⚠️ Throttled on attempt {attempt + 1}. Retrying...")
                    time.sleep(2 ** attempt)
                else:
                    log_frame_error(logger, f"❌ ClientError on attempt {attempt + 1}: {e}")
                    raise e
            except Exception as e:
                log_frame_error(logger, f"❌ Unexpected error on attempt {attempt + 1}: {e}")
                if attempt == bedrock_max_attempts - 1:
                    raise e
                time.sleep(2 ** attempt)

        if not response:
            return {"status": ResponseStatus.FAIL.value, "message": "No response from Bedrock"}

        # LOG: Raw response analysis
        log_frame_info(logger, f"📥 BEDROCK CONVERSE RAW RESPONSE:")
        log_frame_info(logger, f"   📊 Response type: {type(response).__name__}")
        log_frame_info(logger, f"   🔍 Response content type: {response['output']['message']['content'][0]['toolUse']['name']}")

        # Extract tool use arguments
        parsed = cls._extract_tool_use_arguments(response)
        if parsed is not None:
            log_frame_info(logger, f"✅ Tool use extracted successfully")
        else:
            log_frame_warning(logger, f"⚠️ No tool use found, attempting to parse text response...")
            try:
                # Fallback to text if tool use is not found
                raw_text = response['output']['message']['content'][0]['text']
                parsed = raw_text
                log_frame_info(logger, f"✅ Parsed text response.")
            except (KeyError, IndexError):
                log_frame_error(logger, f"❌ Failed to parse any response content.")
                parsed = ""

        usage_data = response.get('usage', {})
        llm_usage = {
            "inputTokens": usage_data.get('inputTokens', 0),
            "outputTokens": usage_data.get('outputTokens', 0),
            "totalTokens": usage_data.get('totalTokens', 0),
            "latencyMs": int((time.time() - start_time) * 1000),
        }

        # LOG: Final image+text output and usage
        log_frame_info(logger, f"📊 BEDROCK CONVERSE FINAL OUTPUT:")
        log_frame_info(logger, f"   📋 Result type: {type(parsed).__name__}")
        log_frame_info(logger, f"   📏 Result size: {len(str(parsed))} characters")
        log_frame_info(logger, f"   🔢 Input tokens: {llm_usage['inputTokens']}")
        log_frame_info(logger, f"   🔢 Output tokens: {llm_usage['outputTokens']}")
        log_frame_info(logger, f"   🔢 Total tokens: {llm_usage['totalTokens']}")
        log_frame_info(logger, f"   ⏱️ Latency: {llm_usage['latencyMs']}ms")
        log_frame_info(logger, f"🎉 NOVA BEDROCK CONVERSE CALL COMPLETED SUCCESSFULLY")

        return {"status": ResponseStatus.SUCCESS.value, "result": parsed, "llm_usage": llm_usage}

    @classmethod
    def _extract_tool_use_arguments(cls, response):
        """
        Extract tool use arguments from the Bedrock Converse API response format.
        """
        try:
            log_frame_debug(logger, f"🔍 EXTRACTING TOOL USE ARGUMENTS:")
            output = response['output']
            message = output['message']
            content = message['content']

            if not content:
                log_frame_debug(logger, "⚠️ No content in message.")
                return None

            # Look for the toolUse block in the content
            for item in content:
                if 'toolUse' in item:
                    tool_use = item['toolUse']
                    log_frame_debug(logger, f"✅ Found toolUse with name: {tool_use['name']}")
                    input_args = tool_use['input']
                    log_frame_debug(logger, f"🔑 Raw input args: {list(input_args.keys())}")
                    return cls._validate_and_sanitize_function_args(input_args)

            log_frame_debug(logger, "❌ No toolUse block found in content.")
            return None
        except Exception as e:
            log_frame_error(logger, f"❌ Error extracting tool use arguments: {e}")
            return None

    @classmethod
    def _validate_and_sanitize_function_args(cls, args_dict):
        """
        Validate and sanitize function call arguments to prevent parsing errors.
        """
        try:
            log_frame_debug(logger, f"🧹 VALIDATING AND SANITIZING FUNCTION ARGS:")
            if not isinstance(args_dict, dict):
                log_frame_error(logger, f"❌ Function args is not a dictionary: {type(args_dict)}")
                return None

            sanitized = {}
            for key, value in args_dict.items():
                if isinstance(value, str):
                    sanitized_value = value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    sanitized_value = ' '.join(sanitized_value.split())
                    sanitized[key] = sanitized_value
                elif isinstance(value, (int, float, bool)) or value is None:
                    sanitized[key] = value
                elif isinstance(value, dict):
                    nested_sanitized = cls._validate_and_sanitize_function_args(value)
                    if nested_sanitized is not None:
                        sanitized[key] = nested_sanitized
                    else:
                        log_frame_error(logger, f"Failed to sanitize nested dict for key: {key}")
                        sanitized[key] = {}
                elif isinstance(value, list):
                    sanitized_list = []
                    for item in value:
                        if isinstance(item, str):
                            sanitized_item = item.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                            sanitized_item = ' '.join(sanitized_item.split())
                            sanitized_list.append(sanitized_item)
                        else:
                            sanitized_list.append(item)
                    sanitized[key] = sanitized_list
                else:
                    str_value = str(value)
                    sanitized_value = str_value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    sanitized_value = ' '.join(sanitized_value.split())
                    sanitized[key] = sanitized_value
                    log_frame_debug(logger, f"Converted {type(value)} to sanitized string for key: {key}")
            
            log_frame_debug(logger, f"✅ Function args validation successful")
            return sanitized
        except Exception as e:
            log_frame_error(logger, f"❌ Error validating function args: {e}")
            return None
