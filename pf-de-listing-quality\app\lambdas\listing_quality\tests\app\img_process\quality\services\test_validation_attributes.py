import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../")))
from app.img_process.quality.services.validation_attributes import ValidationAttributeDetails

import pytest
from unittest.mock import patch, MagicMock



@patch("core.services.config_loader.PreConfigParameters")
def test_validation_attribute_details_defaults(mock_pre_config):
    # Mock return value of pre_configs
    mock_pre_config_instance = MagicMock()
    mock_pre_config_instance.get_quality_validation_secrets.return_value = {
        "pre_config_photo_classification": {"Bathroom": ["sink", "tiles"]},
        "pre_config_room_category": {"Modern": ["gray walls", "sleek"]},
        "pre_config_room_size": {"large": ["spacious", "king bed"]},
    }
    mock_pre_config.return_value = mock_pre_config_instance

    # Import the module after patching so class variables get correct mocked config
    from importlib import reload
    import app.img_process.quality.services.validation_attributes as val_mod
    reload(val_mod)
    ValidationAttributeDetails = val_mod.ValidationAttributeDetails

    expected_keys = {
        "rotation",
        "quality_of_picture",
        "brightness",
        "distortion",
        "is_it_boxed",
        "people_on_photo",
        "watermark",
        "watermark_text",
        "photo_classification",
        "furnished",
        "room_size",
        "is_it_a_render",
        "room_under_construction",
        "room_category",
    }

    assert isinstance(ValidationAttributeDetails.sample_output, dict)
    assert set(ValidationAttributeDetails.sample_output.keys()) == expected_keys
    assert isinstance(ValidationAttributeDetails.pre_config_photo_classification, dict)
    assert isinstance(ValidationAttributeDetails.pre_config_room_category, dict)
    assert isinstance(ValidationAttributeDetails.pre_config_room_size, dict)
    assert ValidationAttributeDetails.image is None
