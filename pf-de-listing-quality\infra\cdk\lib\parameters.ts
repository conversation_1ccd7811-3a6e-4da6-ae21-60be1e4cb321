import * as cdk from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';

export interface ListingQualityParametersProps {
  stage: string;
  resourceNameSuffix: string;
  tags?: { [key: string]: string };
  accountId: string;
  region: string;
}

export class ListingQualityParameters {
  constructor(scope: Construct, id: string, props: ListingQualityParametersProps) {
    const { stage, resourceNameSuffix, tags = {}, accountId, region } = props;
    const paramPrefix = `/listing-quality/${stage}`;
    
    // Environment-specific configurations
    const envConfig = {
      staging: {
        s3ArtifactsBucket: `listing-quality-general-artifacts-${resourceNameSuffix}`,
        s3TrackingBucket: `listing-quality-requests-tracking-${resourceNameSuffix}`,
        dynamodbTable: `listing-quality-requests-tracking-${resourceNameSuffix}`,
        queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality`,
        bedrockConfig: {
          model: "apac.amazon.nova-pro-v1:0",
          temperature: "0"
        }
      },
      production: {
        s3ArtifactsBucket: `listing-quality-general-artifacts-${resourceNameSuffix}`,
        s3TrackingBucket: `listing-quality-requests-tracking-${resourceNameSuffix}`,
        dynamodbTable: `listing-quality-requests-tracking-${resourceNameSuffix}`,
        queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/listing-quality`,
        bedrockConfig: {
          model: "apac.amazon.nova-pro-v1:0",
          temperature: "0"
        }
      }
    };
    
    // Select the appropriate configuration
    const config = stage === 'production' ? envConfig.production : envConfig.staging;
    
    // Helper function to create parameter with tags
    const createParameterWithTags = (id: string, options: ssm.StringParameterProps) => {
      const parameter = new ssm.StringParameter(scope, id, options);
      
      // Apply tags to each parameter
      if (tags) {
        Object.entries(tags).forEach(([key, value]) => {
          cdk.Tags.of(parameter).add(key, value);
        });
      }
      
      return parameter;
    };
    
    // Bedrock Region parameter
    createParameterWithTags('BedrockRegionParam', {
      parameterName: `${paramPrefix}/bedrock/region`,
      stringValue: 'ap-southeast-1', //need to revert when quota increases
      description: 'Bedrock region parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // Bedrock inference config parameter
    createParameterWithTags('BedrockInferenceConfigParam', {
      parameterName: `${paramPrefix}/bedrock/inference/config`,
      stringValue: JSON.stringify({
        max_attempts: "2",
        bedrock_model_name: config.bedrockConfig.model,
        model_version: "bedrock-2023-05-31",
        bedrock_temperature: config.bedrockConfig.temperature,
        bedrock_max_tokens: "1000",
        bedrock_top_p: "1",
        bedrock_top_k: "50",
        input_token_cost:"0.003", 
        output_token_cost: "0.015"
      }),
      description: 'Bedrock inference config parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket parameter for artifacts
    createParameterWithTags('S3ArtifactsBucketParam', {
      parameterName: `${paramPrefix}/s3/artifacts`,
      stringValue: config.s3ArtifactsBucket,
      description: 'S3 bucket parameter for artifacts',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket config directory parameter
    createParameterWithTags('S3ArtifactsConfigDirParam', {
      parameterName: `${paramPrefix}/s3/artifacts/config`,
      stringValue: 'config',
      description: 'S3 bucket config directory parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket prompts directory parameter
    createParameterWithTags('S3ArtifactsPromptsDirParam', {
      parameterName: `${paramPrefix}/s3/artifacts/prompts`,
      stringValue: 'prompts',
      description: 'S3 bucket prompts directory parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // S3 bucket for tracking requests parameter
    createParameterWithTags('S3TrackingBucketParam', {
      parameterName: `${paramPrefix}/s3/tracking`,
      stringValue: config.s3TrackingBucket,
      description: 'S3 bucket for tracking requests parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // SQS process queue parameter
    createParameterWithTags('SQSProcessQueueParam', {
      parameterName: `${paramPrefix}/queue/process`,
      stringValue: `${config.queueUrlBase}-request-processing-queue-${resourceNameSuffix}`,
      description: 'SQS process queue parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // SQS notification queue parameter
    createParameterWithTags('SQSNotificationQueueParam', {
      parameterName: `${paramPrefix}/queue/notification`,
      stringValue: `${config.queueUrlBase}-notifications-success-${resourceNameSuffix}`,
      description: 'SQS notification queue parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // Dynamodb table parameter
    createParameterWithTags('DynamoDBTableParam', {
      parameterName: `${paramPrefix}/dynamodb/table`,
      stringValue: config.dynamodbTable,
      description: 'Dynamodb table parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // Dynamodb ttl parameter
    createParameterWithTags('DynamoDBTtlParam', {
      parameterName: `${paramPrefix}/dynamodb/ttl`,
      stringValue: '48',
      description: 'Dynamodb ttl parameter',
      tier: ssm.ParameterTier.STANDARD,
    });
    
    // LLM images process limit
    createParameterWithTags('ImagesLimitParam', {
      parameterName: `${paramPrefix}/images/limit`,
      stringValue: '15',
      description: 'LLM images process limit',
      tier: ssm.ParameterTier.STANDARD,
    });

    // Gemini API Key parameter
    createParameterWithTags('GeminiApiKeyParam', {
      parameterName: `${paramPrefix}/gemini/api_key`,
      stringValue: 'CHANGE_ME',  // This should be set manually in AWS SSM Parameter Store
      description: 'Gemini API Key parameter',
      tier: ssm.ParameterTier.STANDARD,
    });

    // LLM Provider parameter
    createParameterWithTags('LlmProviderParam', {
      parameterName: `${paramPrefix}/llm/provider`,
      stringValue: 'bedrock',  // Default to bedrock, can be changed to 'gemini' when needed
      description: 'LLM Provider parameter (bedrock or gemini)',
      tier: ssm.ParameterTier.STANDARD,
    });
  }
}
