{key}: What do you see in the image?.
### Follow the below instructions to generate the {key} details ###
- If a kitchen is enclosed by glass doors , it should be considered as closed kitchen.
- If more than one value from the list {value} is present in the image, 
    choose the one that matches the major portion of the image.
- First analyse every value present in the list before picking the final value.
- Do not pick value which comes first in the list , choose the value from the list which matches the most and has highest percentage of match with the image.
- Pick only one value only from the list - {value}. Do not pick any value other than what is provided in the list.
- If you can not classify the property in the picture, then pick any value only from the list, which basically means "no {key}" or "{key} not present" or "not available", if not, then return empty string ""
- If the image can not be explained by the values in the list, then return empty string ""