AWSTemplateFormatVersion: "2010-09-09"
Transform: "AWS::Serverless"
Description: "PF FastAPI App"

Resources:
  PFFastAPIFunction:
    Type: "AWS::Serverless::Function"
    Properties:
      Handler: "main.handler"  # Replace with your handler
      Runtime: "python3.11"
      CodeUri: "."
      Timeout: 900
      Environment:
        Variables:
          PYTHONDONTWRITEBYTECODE: 1
          PYTHONUNBUFFERED: 1
          CONCURRENT_WORKERS: 15
          SQLALCHEMY_DB_URL: "postgresql://postgres:<EMAIL>:5432/pf_goml_dev"
          AWS_DEFAULT_REGION_PARAM: "AWS_DEFAULT_REGION"
          DEFAULT_REGION_VALUE: "us-east-1"
          AWS_ACCESS_KEY_ID: "********************"
          AWS_SECRET_ACCESS_KEY: "oaymXOsrJn21rpElZalUtEKf2i4OEy1kgQ3Pwi6d"
          AWS_SESSION_TOKEN: "IQoJb3JpZ2luX2VjEDgaCXVzLWVhc3QtMSJHMEUCIQDLTX6mHd1F97xguRN0+cG8WEs/ukol+Y/yKsH9KuF8hAIgbkYf4sYFldMtn4rAH45zVqiaBVnaP8q1OPW/WnWdGt0qqwMIkf//////////ARAAGgwwMjUwNjYyODQzMzEiDAo3NeBzk9hnVDTKiCr/AlVeyLBSaT+XKItIA3O8Kj39Mmfn8MV+xsH+Ad7TtR4sHwqAuwx4O6owHUj/esaHRHOjY7JP9WYpWF2qaecu8OCE7QoJx531hcqemYCf3jXQfX9diTFAXL/oc0kLfKnCgcRBkknf35hqf7/SZDpMinhbHETV6UxLbIBXSdv7AEltsJSWAGmRskCTayMMK5XfCzUxBPK14Ue/v4AA30e4wCOAj+JfQvr/QbNVRUrt9jxfHsJPOk+5cgVPjRjN8prDD2sJbV90RYuYR+9ubBVtwMzr05RHOeHurANUa/bvfvMGeahSyRWVQ8ZsQk9vrmXJExPWLlljfXIyehlgLzwExaVxtF+Giu1MMuU+K94DQkWIX8mDMf1g2/l+kilXuFFxHpUVN4+q3n4ZvApc/OTQRX3DW6Mnodjqcb+sW5nAlgDbqTCcGbhxfitevM5fdOynW4o1BxBRQIlqePJFvK80nind55aDQ5k+M6QTHGEJnfZXOBN/NRATF0B2o0eUYITDMK7t8L4GOqYBbitUxpkd9FQJZqpsEnDzoC6KW0Dl1NwJ+HSZQ8jMYFgEPZfrlPxCwL/CX3UwXmvBeVBpMCz22FavuaI7ZC81/QxAYAGCvYOwtBoy6N7zVqA6dCJ2BjuYt/vtIqWMDDtpMISwEPuq057QeW0VHMKO75MvG0VNZUayyXf4SYFN5+UtstO7eCoVyZgvKR2Vw85kHMpTVzg6Xm1O6M9GExTd5+xSZlL8Qw=="
      Events:
        MyApi:
          Type: "Api"
          Properties:
            Path: "/"
            Method: "POST"