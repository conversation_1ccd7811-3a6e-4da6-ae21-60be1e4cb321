from io import BytesIO

import cv2
import numpy as np
import requests
from PIL import Image

from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_error,
    log_frame_debug,
    log_frame_warning,
)

# Initialize a logger specific to this module
logger = get_logger(__name__)


def calculate_brightness(gray):
    """
    Evaluates image brightness by calculating the mean pixel intensity.
    A value close to 127 (midpoint of 0-255) is considered ideal.
    Higher brightness values are acceptable as long as they are not extreme.
    """

    try:
        log_frame_info(logger, "Calculating image brightness.")
        
        # Validate that the input is a NumPy array and not None
        if gray is None or not isinstance(gray, np.ndarray):
            log_frame_warning(
                logger,
                message="Invalid input: Expected a NumPy array but got.",
                gray_type=type(gray),
            )
            return None

        # Check if the array is empty
        if gray.size == 0:
            log_frame_warning(
                logger,
                message="Empty image array received. Unable to calculate brightness.",
            )
            return None

        # Compute the mean pixel value of the grayscale image
        brightness = np.mean(gray)
        log_frame_debug(logger, message="Mean pixel intensity.", brightness=brightness)

        # Calculate brightness score:
        # The closer the brightness is to 127, the higher the score (max 10, min 0)
        score = np.clip(10 - abs(127 - brightness) / 12.7, 0, 10)
        log_frame_info(logger, message="Brightness score calculated.", score=score)

        return score

    except Exception as e:
        # Log any unexpected errors during calculation
        log_frame_error(
            logger, message="Unexpected error in brightness calculation.", error=str(e)
        )

        return None


def calculate_sharpness(gray):
    """
    Evaluate image sharpness using the variance of the Laplacian.
    A higher sharpness score means the image is sharper.

    :param gray: Grayscale image as a NumPy array.
    :return: Sharpness score (1-10 scale), or None if an error occurs.
    """

    try:
        log_frame_info(logger, "Calculating image sharpness.")

        # Validate that the input is a NumPy array and not None
        if gray is None or not isinstance(gray, np.ndarray):
            log_frame_warning(
                logger,
                message="Invalid input: Expected a NumPy array but got",
                color=type(gray),
            )

            return None

        # Check if the array is empty
        if gray.size == 0:
            log_frame_warning(
                logger,
                message="Empty image array received. Unable to calculate sharpness.",
            )
            return None

        # Calculate the variance of the Laplacian (a measure of image sharpness)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        log_frame_debug(
            logger, message="Laplacian variance.", laplacian_var=laplacian_var
        )

        # Convert variance to a sharpness score on a scale from 0 to 10
        if 0 < laplacian_var <= 100:
            sharpness_score = np.interp(laplacian_var, [0, 100], [0, 2])
        elif 100 < laplacian_var <= 200:
            sharpness_score = np.interp(laplacian_var, [100, 200], [2, 4])
        elif 200 < laplacian_var <= 400:
            sharpness_score = np.interp(laplacian_var, [200, 400], [4, 6])
        elif 400 < laplacian_var <= 600:
            sharpness_score = np.interp(laplacian_var, [400, 600], [6, 8])
        elif laplacian_var > 600:
            # Cap the variance at 1000 to avoid extreme values
            sharpness_variance = min(laplacian_var, 1000)  
            sharpness_score = np.interp(laplacian_var, [600, 1000], [8, 10])

        # Log the final computed sharpness score
        log_frame_info(
            logger,
            message="Sharpness score calculated.",
            sharpness_score=sharpness_score,
        )
        return sharpness_score

    except Exception as e:
        # Handle unexpected errors and log them
        log_frame_error(
            logger, message="Unexpected error in sharpness calculation.", error=str(e)
        )
        return None


def calculate_distortion(image):
    """
    Evaluates image distortion by detecting straight lines using Hough Transform.
    A higher distortion score indicates more distortion.

    :param image: Grayscale image as a NumPy array.
    :return: Distortion score (0-10 scale), or None if an error occurs.
    """

    try:
        # Validate input: Must be a non-empty NumPy array
        if image is None or not isinstance(image, np.ndarray):
            log_frame_warning(
                logger,
                message="Invalid input: Expected a NumPy array but got.",
                image_type=type(image),
            )

            return None

        if image.size == 0:
            log_frame_warning(
                logger,
                message="Empty image array received. Unable to calculate distortion.",
            )

            return None

        # Get image dimensions
        height, width = image.shape[:2]
        log_frame_debug(
            logger,
            message="Image dimensions - Height, Width",
            height=height,
            width=width,
        )

        # Detect straight lines in the image using the Probabilistic Hough Transform
        straight_lines = cv2.HoughLinesP(
            image, 1, np.pi / 180, threshold=100, minLineLength=20, maxLineGap=10
        )

        # If no lines are found, assume no distortion and return the maximum score
        if straight_lines is None or len(straight_lines) == 0:
            log_frame_info(
                logger, "No distortion detected, returning maximum score (10)."
            )
            return 10  # Maximum score if no distortion is detected

        # Count the number of detected lines
        line_count = len(straight_lines)
        log_frame_debug(
            logger, message="Detected straight lines.", line_count=line_count
        )

        # Guard against division by zero if image dimensions are invalid
        if (height + width) == 0:
            log_frame_error(
                logger,
                message="Invalid image dimensions (height + width = 0), returning default score.",
            )
            return None

        # Compute a distortion ratio based on the number of lines and image size
        distortion_score = line_count / (height + width)

        # Calculate final score by inverting distortion score, clamping it between 0 and 10
        final_score = 10 - np.clip(
            1 / max(distortion_score, 1e-6), 0, 10
        )  # Avoid division by zero

        # Log the final score and return
        log_frame_info(
            logger, message="Distortion score calculated: ", final_score=final_score
        )
        return final_score

    except Exception as e:
        # Log unexpected errors for debugging purposes
        log_frame_error(
            logger, message="Unexpected error in distortion calculation.", error=str(e)
        )
        return None


def calculate_resolution(image):
    """
    Evaluates image resolution by comparing it to a reference resolution.
    Higher resolution is better, and the score is scaled to a 0-10 range.

    :param image: Input image as a NumPy array.
    :return: Resolution score (0-10 scale), or None if an error occurs.
    """

    try:
        # Validate input type and content
        if image is None or not isinstance(image, np.ndarray):
            log_frame_warning(
                logger,
                message="Invalid input: Expected a NumPy array but got ",
                image_type=type(image),
            )
            return None

        # Check if image is empty
        if image.size == 0:
            log_frame_warning(
                logger,
                message="Empty image array received. Unable to calculate resolution.",
            )

            return None

        # Extract image height and width
        height, width = image.shape[:2]

        # Validate image dimensions
        if height == 0 or width == 0:
            log_frame_warning(
                logger, message="Invalid image dimensions (height or width is zero)."
            )
            return None
        
        # Log image dimensions for debugging
        log_frame_debug(
            logger,
            message="Image dimensions - Height, Width",
            height=height,
            width=width,
        )

        # Define reference resolution to compare against (standard high-quality resolution)
        reference_width, reference_height = 1200, 800
        if reference_width == 0 or reference_height == 0:
            log_frame_error(logger, message="Reference resolution cannot be zero.")
            return None

        # Calculate resolution score as a ratio of current resolution to reference resolution
        resolution_score = (width * height) / (reference_width * reference_height) * 10

        # Ensure the score stays within 0-10 range using clipping
        final_score = np.clip(resolution_score, 0, 10)

        # Log the final calculated score
        log_frame_info(
            logger, message="Resolution score calculated.", final_score=final_score
        )
        return final_score

    except Exception as e:
        # Handle unexpected errors gracefully and log them
        log_frame_error(
            logger, message="Unexpected error in resolution calculation.", error=str(e)
        )
        return None


def image_quality(resp_image):
    """
    Evaluates the quality of an image based on brightness, sharpness, distortion, and resolution.

    :param resp_image: Response object containing image content.
    :return: Dictionary containing individual and total image quality scores.
    """

    try:
        # Step 1: Validate input image object
        if resp_image is None or not hasattr(resp_image, "content"):
            log_frame_warning(
                logger,
                message="Invalid image response. Expected an object with 'content'.",
            )
            return {}

        # Step 2: Convert image content (bytes) into a NumPy array for decoding
        image_data = np.asarray(bytearray(resp_image.content), dtype="uint8")
        
        # Step 3: Decode the image data into a BGR image using OpenCV
        image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)

        if image is None:
            log_frame_warning(logger, message="Failed to decode image.")
            return {}

        log_frame_info(logger, "Image successfully decoded.")

        # Step 4: Convert decoded image to a NumPy array (sanity check for empty image)
        image_array = np.array(image)
        if image_array.size == 0:
            log_frame_warning(logger, message="Decoded image is empty.")
            return {}

        try:
            # Step 5: Convert BGR image to grayscale for analysis
            gray_image = cv2.cvtColor(image_array, cv2.COLOR_BGR2GRAY)
            log_frame_debug(logger, message="Image converted to grayscale.")
        except Exception as e:
            log_frame_error(
                logger, message="Failed to convert image to grayscale.", error=str(e)
            )
            return {}

        # Step 6: Compute quality metrics using individual scoring functions
        brightness_score = calculate_brightness(gray_image) or 0
        log_frame_info(
            logger, message="Brightness score.", brightness_score=brightness_score
        )

        sharpness_score = calculate_sharpness(gray_image) or 0
        log_frame_info(
            logger, message="Sharpness score.", sharpness_score=sharpness_score
        )

        distortion_score = calculate_distortion(gray_image) or 0
        log_frame_info(
            logger, message="Distortion score.", distortion_score=distortion_score
        )

        resolution_score = calculate_resolution(gray_image) or 0
        log_frame_info(
            logger, message="Resolution score.", resolution_score=resolution_score
        )

        # Step 7: Calculate total quality score using weighted sum of individual scores
        total_quality = (
            (brightness_score * 0.15)
            + (sharpness_score * 0.40)
            + (distortion_score * 0.30)
            + (resolution_score * 0.15)
        )
        log_frame_info(
            logger, message="Total image quality score.", total_quality=total_quality
        )

        # Step 8: Return rounded scores for better readability
        return {
            "brightness_score": round(brightness_score, 2),
            "sharpness_score": round(sharpness_score, 2),
            "distortion_score": round(distortion_score, 2),
            "resolution_score": round(resolution_score, 2),
            "total_quality": round(total_quality, 2),
        }

    except Exception as e:
        # Catch and log any unexpected errors
        log_frame_error(
            logger, message="Unexpected error in image quality analysis.", error=str(e)
        )
        return {}


def get_boxed(image_path):
    """
    Checks if an image has a white or black border.

    :param image_path: URL of the image.
    :return: True if the image has a significant border, False otherwise.
    """

    try:
        # Log and attempt to fetch the image from the provided URL
        log_frame_info(logger, message="Fetching image from.", image_path=image_path)
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                      "(KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
        }
        response = requests.get(
            image_path, headers=headers, timeout=10
        )

        # If request fails (non-200 status), log error and return False
        if response.status_code != 200:
            log_frame_error(
                logger,
                message="Failed to fetch image.",
                status_code=response.status_code,
            )
            return False

        # Open the image from the response content using Pillow
        image = Image.open(BytesIO(response.content))
        log_frame_info(logger, "Image successfully retrieved and loaded.")

        # Extract image dimensions (width, height)
        width, height = image.size

        # Check for invalid image dimensions
        if width == 0 or height == 0:
            log_frame_warning(
                logger, message="Invalid image dimensions. Returning False."
            )
            return False

        # Convert the image to grayscale for easier pixel intensity analysis
        grayscale = image.convert("L")

        # Extract the top row, bottom row, left column, and right column pixels
        top_pixels = list(grayscale.crop((0, 0, width, 1)).getdata())
        bottom_pixels = list(grayscale.crop((0, height - 1, width, height)).getdata())
        left_pixels = list(grayscale.crop((0, 0, 1, height)).getdata())
        right_pixels = list(grayscale.crop((width - 1, 0, width, height)).getdata())

        # Set the threshold for considering a border as white or black
        threshold = 0.95
        pixel_count = len(left_pixels)

        # Helper function to detect white border
        def is_white(pixels):
            return sum(1 for p in pixels if p == 255) / pixel_count > threshold

        # Helper function to detect black border
        def is_black(pixels):
            return sum(1 for p in pixels if p == 0) / pixel_count > threshold

        # Check if any of the borders are mostly white
        if any(
            is_white(p) for p in [left_pixels, right_pixels, top_pixels, bottom_pixels]
        ):
            log_frame_info(logger, "Image has a white border.")
            return True

        # Check if any of the borders are mostly black
        if any(
            is_black(p) for p in [left_pixels, right_pixels, top_pixels, bottom_pixels]
        ):
            log_frame_info(logger, "Image has a black border.")
            return True

        # If no significant border is detected, log and return False
        log_frame_info(logger, "Image has no significant border.")
        return False
    # Handle image fetching errors
    except requests.exceptions.RequestException as e:
        log_frame_error(logger, message="Error fetching image.", error=str(e))
    # Handle image loading/conversion errors
    except IOError as e:
        log_frame_error(logger, message="Error processing image.", error=str(e))
    # Catch-all for any other unexpected errors
    except Exception as e:
        log_frame_error(logger, message="Unexpected error in get_boxed.", error=str(e))
    # In case of any failure, return False as a fallback
    return False
