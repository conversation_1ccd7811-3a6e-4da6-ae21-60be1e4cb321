import pytest
import boto3
import yaml
from moto import mock_aws
import json
from pathlib import Path
from app.img_process.quality.schema import ImageDetails
from core.services.prompt_loads import PromptLoader

TABLE_NAME = "test_request_tracking"


MOCK_BUCKET = "test-artifact-bucket"
MOCK_CONFIG_DIR = "config"
MOCK_CONFIG_KEY = f"{MOCK_CONFIG_DIR}/config.yaml"

MOCK_YAML_CONTENT = {
    "QUALITY_VALIDATION_CONFIG_PARAMETERS": {
        "pre_config_competitors_list": [
            "bayut",
            "dubbizzle",
            "aqar",
            "aqarmap",
            "elbayt",
            "re/max",
            "wasalt",
            "skyloov",
        ]
    }
}


MOCK_BUCKET = "mock-bucket"
PROMPT_DIR = "validate_image_quality"

from dotenv import load_dotenv

@pytest.fixture(scope="session", autouse=True)
def load_test_env():
    env_path = Path(__file__).resolve().parent / "../.env.test"
    load_dotenv(dotenv_path=env_path, override=True)
    
@pytest.fixture(scope="function")
def dynamodb_mock():
    with mock_aws():
        # Set up DynamoDB
        dynamodb = boto3.resource("dynamodb")
        table = dynamodb.create_table(
            TableName=TABLE_NAME,
            KeySchema=[{"AttributeName": "RequestID", "KeyType": "HASH"}],
            AttributeDefinitions=[{"AttributeName": "RequestID", "AttributeType": "S"}],
            ProvisionedThroughput={"ReadCapacityUnits": 5, "WriteCapacityUnits": 5},
        )
        table.wait_until_exists()

        # Monkeypatch the global table object used in the actual code
        import core.utils.utils as utils

        utils.dynamodb_table_name = table

        yield table


@pytest.fixture
def sample_images():
    return [ImageDetails(id=i, src=f"https://img.com/{i}.jpg") for i in range(1, 6)]


@pytest.fixture
def s3_with_yaml(monkeypatch):
    with mock_aws():  # covers S3 + DynamoDB etc.
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=MOCK_BUCKET)

        yaml_data = yaml.dump(MOCK_YAML_CONTENT)
        s3.put_object(Bucket=MOCK_BUCKET, Key=MOCK_CONFIG_KEY, Body=yaml_data)

        # Monkeypatch config values to use test bucket/dir
        from core.services import config_loader

        monkeypatch.setitem(config_loader.config, "s3_bucket_artifacts", MOCK_BUCKET)
        monkeypatch.setitem(
            config_loader.config, "s3_bucket_artifacts_config", MOCK_CONFIG_DIR
        )

        yield  # No need to return anything if not directly used


@pytest.fixture
def s3_prompts(tmp_path_factory, monkeypatch):
    with mock_aws():
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=MOCK_BUCKET)

        # Upload prompt files
        sample_prompt_text = "This is a sample prompt text for detection."
        prompt_file_key = f"prompts/{PROMPT_DIR}/detect_watermark_text.txt"
        s3.put_object(Bucket=MOCK_BUCKET, Key=prompt_file_key, Body=sample_prompt_text)

        # Create a temporary prompts.json file
        prompts_dict = {
            "detect_watermark_text": f"{PROMPT_DIR}/detect_watermark_text.txt"
        }

        prompts_path = tmp_path_factory.mktemp("prompts") / "prompts.json"
        with open(prompts_path, "w") as f:
            json.dump(prompts_dict, f)

        # Patch config to return our test bucket
        import config.config as config_module

        config_module.config.config["s3_bucket_artifacts"] = MOCK_BUCKET

        yield str(prompts_path)


@pytest.mark.asyncio
async def test_prompt_loader_s3_download(s3_prompts):
    await PromptLoader.load_prompts(s3_prompts)

    result = PromptLoader.get_prompt("detect_watermark_text")

    assert result == "This is a sample prompt text for detection."



