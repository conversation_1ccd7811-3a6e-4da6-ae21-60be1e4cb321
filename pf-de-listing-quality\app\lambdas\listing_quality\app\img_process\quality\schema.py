from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from typing import Optional, List


class ImageQualityBase(BaseModel):
    img_id: int
    listing_id: Optional[int] = None
    og_url: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ImageQualityCreate(ImageQualityBase):
    pass


class ImageQuality(ImageQualityBase):
    id: int

    class Config:
        from_attributes = True


class ImageProcessParametersBase(BaseModel):
    param_name: str
    param_type: str
    param_max_value: Optional[int] = None
    param_min_value: Optional[int] = None
    param_prompt: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ImageProcessParametersCreate(ImageProcessParametersBase):
    pass


class ImageProcessParameters(ImageProcessParametersBase):
    id: int

    class Config:
        from_attributes = True


class ImageParametersJunctionBase(BaseModel):
    image_db_id: int
    param_id: int
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ImageParametersJunctionCreate(ImageParametersJunctionBase):
    pass


class ImageParametersJunction(ImageParametersJunctionBase):
    id: int

    class Config:
        from_attributes = True


### temp demo code ###
class ImageDetails(BaseModel):
    id: int
    src: HttpUrl


class PhotoAttributes(BaseModel):
    customer_type: Optional[str] = None
    type: Optional[str] = None
    listing_id: Optional[int] = None
    country_code: Optional[str] = None
    submission_id: Optional[str] = None
    message_handle: Optional[str] = None
    country_code: Optional[str] = None
    validate_params: List[int] = None
    images: List[ImageDetails]
    custom_input_list: Optional[dict] = {}


class ImageQualityCompetitorslist:
    preconfig_competitors_list = [
        "bayut",
        "dubbizzle",
        "aqar",
        "aqarmap",
        "elbayt",
        "re/max",
        "wasalt",
        "skyloov",
    ]
