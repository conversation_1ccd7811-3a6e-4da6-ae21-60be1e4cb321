import os
import json
import boto3
import pytest
from unittest.mock import patch, MagicMock
from moto import mock_aws

os.environ["AWS_DEFAULT_REGION"] = "us-east-1"

# Import the lambda function
import sys
sys.path.append('.')  # Adjust if needed to find your lambda code

mock_ssm_client = MagicMock()
with patch('boto3.client', return_value=mock_ssm_client):
    from request_invocation import lambda_handler, fetch_messages, process_messages, invoke_lambda_api

@pytest.fixture
def mock_ssm():
    """Create a mock SSM client."""
    # Reset the mock before each test
    mock_ssm_client.reset_mock()
    return mock_ssm_client

# Constants for testing
TEST_QUEUE_URL = "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue"
TEST_LAMBDA_ARN = "arn:aws:lambda:us-east-1:123456789012:function:test-api-lambda"
TEST_LAMBDA_NAME = "test-api-lambda"

# Set up environment variables for testing
@pytest.fixture(autouse=True)
def mock_env_variables():
    with patch.dict(os.environ, {
        "REQUEST_PROCESSING_QUEUE_URL": TEST_QUEUE_URL,
        "LISTING_QUALITY_API_LAMBDA": TEST_LAMBDA_NAME
    }):
        yield

# Mock SQS service
@pytest.fixture
def sqs():
    with mock_aws():
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        
        # Create test queue
        queue = sqs_client.create_queue(QueueName='test-queue')
        queue_url = queue['QueueUrl']
        
        yield sqs_client

# Mock Lambda service
@pytest.fixture
def lambda_mock():
    with mock_aws():
        lambda_client = boto3.client('lambda', region_name='us-east-1')        
        yield lambda_client

# Helper function to add messages to the queue
def populate_queue(sqs_client, messages):
    for msg in messages:
        sqs_client.send_message(
            QueueUrl=TEST_QUEUE_URL,
            MessageBody=json.dumps(msg)
        )

# Test fetch_messages with error
@patch('request_invocation.sqs_client.receive_message')
def test_fetch_messages_error(mock_receive):
    # Mock SQS error
    mock_receive.side_effect = Exception("Test SQS error")
    
    # Call the function
    result = fetch_messages()
    
    # Should return empty list on error
    assert result == []

# Test invoke_lambda_api success
@patch('request_invocation.lambda_client.invoke')
def test_invoke_lambda_api_success(mock_invoke):
    # Mock data
    test_payload = {
        "resource": "/validate_quality",
        "path": "/validate_quality",
        "httpMethod": "POST",
        "body": json.dumps({"type": "IMAGE_VALIDATION", "submission_id": "test-123"})
    }
    
    submission_id = "test-123"
    
    # Call the function
    invoke_lambda_api(test_payload, submission_id)
    
    # Verify lambda was invoked with correct parameters
    mock_invoke.assert_called_once()
    call_args = mock_invoke.call_args[1]
    assert call_args['InvocationType'] == "Event"
    assert isinstance(call_args['Payload'], str)
    # Validate payload is proper JSON
    json.loads(call_args['Payload'])

# Test invoke_lambda_api error
@patch('request_invocation.lambda_client.invoke')
def test_invoke_lambda_api_error(mock_invoke):
    # Mock lambda error
    mock_invoke.side_effect = Exception("Test Lambda invoke error")
    
    # Test data
    test_payload = {
        "resource": "/validate_quality",
        "path": "/validate_quality",
        "httpMethod": "POST",
        "body": json.dumps({"type": "IMAGE_VALIDATION", "submission_id": "test-123"})
    }
    
    submission_id = "test-123"
    
    # Call the function (should not raise exception)
    invoke_lambda_api(test_payload, submission_id)
    
    # Function should log the error but not raise it further

# FIX: This test was potentially causing an infinite loop
# Added a patch to make sure process_messages exits after processing the messages
@patch('request_invocation.fetch_messages')
@patch('request_invocation.invoke_lambda_api')
def test_process_messages_valid(mock_invoke, mock_fetch):
    # Mock messages - modified to ensure loop exits correctly
    messages = [
        {
            "MessageId": "msg-1",
            "ReceiptHandle": "receipt-1",
            "Body": json.dumps({
                "submission_id": "test-123",
                "type": "IMAGE_VALIDATION",
                "data": {"url": "https://example.com/image.jpg"}
            })
        }
    ]
    
    # Set up the side effect to return messages once, then always return empty list
    mock_fetch.side_effect = [messages, []]
    
    # Use patch.object to intercept internal loop condition
    with patch('request_invocation.process_messages', wraps=process_messages) as wrapped_process:
        # Call the function directly
        process_messages()
    
    # Verify lambda was invoked with correct parameters
    mock_invoke.assert_called_once()
    call_args = mock_invoke.call_args
    
    # Check payload
    payload = call_args[0][0]
    assert payload["resource"] == "/validate_quality"
    assert payload["path"] == "/validate_quality"
    assert payload["httpMethod"] == "POST"
    
    # Check body
    body = json.loads(payload["body"])
    assert body["submission_id"] == "test-123"
    assert body["type"] == "IMAGE_VALIDATION"
    assert body["message_handle"] == "receipt-1"

# Fix other tests with similar pattern to prevent potential hangs
@patch('request_invocation.fetch_messages')
@patch('request_invocation.invoke_lambda_api')
def test_process_messages_invalid_json(mock_invoke, mock_fetch):
    # Mock fetch_messages to ensure it terminates
    mock_fetch.side_effect = [
        [
            {
                "MessageId": "msg-1",
                "ReceiptHandle": "receipt-1",
                "Body": "{invalid json"
            }
        ],
        []  # Empty list to ensure the loop terminates
    ]
    
    # Call the function
    process_messages()
    
    # Verify lambda was not invoked
    mock_invoke.assert_not_called()

@patch('request_invocation.fetch_messages')
@patch('request_invocation.invoke_lambda_api')
def test_process_messages_unsupported_type(mock_invoke, mock_fetch):
    # Mock fetch_messages to ensure it terminates
    mock_fetch.side_effect = [
        [
            {
                "MessageId": "msg-1",
                "ReceiptHandle": "receipt-1",
                "Body": json.dumps({
                    "submission_id": "test-123",
                    "type": "UNSUPPORTED_TYPE",
                    "data": {"url": "https://example.com/image.jpg"}
                })
            }
        ],
        []  # Empty list to ensure the loop terminates
    ]
    
    # Call the function
    process_messages()
    
    # Verify lambda was not invoked
    mock_invoke.assert_not_called()

# Fix for unexpected error test
@patch('request_invocation.fetch_messages')
@patch('request_invocation.json.loads')
def test_process_messages_unexpected_error(mock_json, mock_fetch):
    # Mock fetch_messages to ensure it terminates
    mock_fetch.side_effect = [
        [
            {
                "MessageId": "msg-1",
                "ReceiptHandle": "receipt-1",
                "Body": json.dumps({
                    "submission_id": "test-123",
                    "type": "IMAGE_VALIDATION"
                })
            }
        ],
        []  # Empty list to ensure the loop terminates
    ]
    
    # Force unexpected error on first call, then normal behavior
    original_loads = json.loads
    mock_json.side_effect = [Exception("Unexpected test error"), original_loads]
    
    # Call the function (should not raise exception)
    process_messages()

# Test lambda_handler
@patch('request_invocation.process_messages')
def test_lambda_handler(mock_process):
    # Call the handler
    response = lambda_handler({}, {})
    
    # Verify process_messages was called
    mock_process.assert_called_once()
    
    # Verify response
    assert response["status"] == "Processed all messages"

# Integration test with mocked AWS services
# Adding a patch to limit fetch_messages calls
@patch('request_invocation.lambda_client.invoke')
def test_integration(mock_invoke, sqs, lambda_mock):
    # Prepare test messages
    test_messages = [
        {
            "submission_id": "test-submission-1",
            "type": "IMAGE_VALIDATION",
            "data": {"url": "https://example.com/image1.jpg"}
        },
        {
            "submission_id": "test-submission-2",
            "type": "INVALID_TYPE",  # This should be skipped
            "data": {"url": "https://example.com/image2.jpg"}
        }
    ]
    
    # Add messages to the queue
    for msg in test_messages:
        sqs.send_message(
            QueueUrl=TEST_QUEUE_URL,
            MessageBody=json.dumps(msg)
        )
    
    # Ensure we exit after one loop by patching fetch_messages
    with patch('request_invocation.fetch_messages') as mock_fetch:
        # First call returns our test messages, subsequent calls return empty list
        mock_fetch.side_effect = lambda: (
            sqs.receive_message(
                QueueUrl=TEST_QUEUE_URL,
                MaxNumberOfMessages=10
            ).get('Messages', [])
            if not hasattr(mock_fetch, 'called')
            else (setattr(mock_fetch, 'called', True) or [])
        )
        
        # Call the handler
        response = lambda_handler({}, {})
        
        # Check the response
        assert response["status"] == "Processed all messages"

# Test handling of mixed message types
@patch('request_invocation.fetch_messages')
@patch('request_invocation.invoke_lambda_api')
def test_process_mixed_message_types(mock_invoke, mock_fetch):
    # Mock messages with a mix of valid and invalid types
    mock_fetch.side_effect = [
        [
            {
                "MessageId": "msg-1",
                "ReceiptHandle": "receipt-1",
                "Body": json.dumps({
                    "submission_id": "test-123",
                    "type": "IMAGE_VALIDATION",
                    "data": {"url": "https://example.com/image.jpg"}
                })
            },
            {
                "MessageId": "msg-2",
                "ReceiptHandle": "receipt-2",
                "Body": json.dumps({
                    "submission_id": "test-456",
                    "type": "UNSUPPORTED_TYPE",
                    "data": {"url": "https://example.com/doc.pdf"}
                })
            }
        ],
        []  # Second call returns empty list to break the loop
    ]
    
    # Call the function
    process_messages()
    
    # Verify lambda was invoked only once for the valid message
    assert mock_invoke.call_count == 1
    
    # Check the invocation was for the valid message
    call_args = mock_invoke.call_args
    payload = call_args[0][0]
    body = json.loads(payload["body"])
    assert body["submission_id"] == "test-123"
    assert body["type"] == "IMAGE_VALIDATION"