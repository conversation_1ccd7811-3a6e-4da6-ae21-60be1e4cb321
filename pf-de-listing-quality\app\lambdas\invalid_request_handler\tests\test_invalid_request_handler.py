import pytest
import json
import os
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock, DEFAULT

# We need to patch boto3 and SSM before importing the lambda function
@pytest.fixture(scope="module", autouse=True)
def mock_aws_credentials():
    """Mock AWS credentials for boto3"""
    with patch.dict(os.environ, {
        "AWS_ACCESS_KEY_ID": "testing",
        "AWS_SECRET_ACCESS_KEY": "testing",
        "AWS_SECURITY_TOKEN": "testing",
        "AWS_SESSION_TOKEN": "testing",
        "AWS_DEFAULT_REGION": "us-east-1",
        "ENV": "staging"
    }):
        yield

@pytest.fixture(scope="module")
def mock_ssm_before_import():
    """Mock SSM before lambda function import"""
    with patch("boto3.client") as mock_boto3_client:
        # Configure SSM mock
        mock_ssm = MagicMock()
        mock_ssm.get_parameters.return_value = {
            'Parameters': [
                {'Name': '/listing-quality/staging/s3/tracking', 'Value': 'test-bucket'},
                {'Name': '/listing-quality/staging/dynamodb/table', 'Value': 'test-table'},
                {'Name': '/listing-quality/staging/dynamodb/ttl', 'Value': '2'},
                {'Name': '/listing-quality/staging/queue/notification', 'Value': 'test-queue-url'}
            ]
        }
        mock_boto3_client.return_value = mock_ssm
        yield mock_ssm

# Import the lambda function only after patching AWS credentials and SSM
@pytest.fixture(scope="module")
def lambda_module(mock_ssm_before_import):
    with patch("boto3.resource"), patch("boto3.client"):
        import invalid_request_handler
        yield invalid_request_handler

def test_fetch_ssm_parameters(lambda_module):
    """Test fetch_ssm_parameters function"""
    with patch.object(lambda_module, 'ssm_client') as mock_ssm:
        mock_ssm.get_parameters.return_value = {
            'Parameters': [
                {'Name': '/listing-quality/staging/s3/tracking', 'Value': 'test-bucket'},
                {'Name': '/listing-quality/staging/dynamodb/table', 'Value': 'test-table'},
                {'Name': '/listing-quality/staging/dynamodb/ttl', 'Value': '2'},
                {'Name': '/listing-quality/staging/queue/notification', 'Value': 'test-queue-url'}
            ]
        }
        
        result = lambda_module.fetch_ssm_parameters()
        
        assert mock_ssm.get_parameters.called
        assert result == {
            '/listing-quality/staging/s3/tracking': 'test-bucket',
            '/listing-quality/staging/dynamodb/table': 'test-table',
            '/listing-quality/staging/dynamodb/ttl': '2',
            '/listing-quality/staging/queue/notification': 'test-queue-url'
        }

def test_log_failure_to_dynamodb(lambda_module):
    """Test log_failure_to_dynamodb function"""
    with patch.object(lambda_module, 'dynamodb_table') as mock_table, \
         patch('datetime.datetime') as mock_datetime:
        # Mock datetime.now() to return a fixed datetime
        mock_now = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        mock_datetime.now.return_value = mock_now
        # Make sure timestamp() works on our mock
        mock_now_instance = MagicMock()
        mock_now_instance.timestamp.return_value = 1672574400  # 2023-01-01 12:00:00 UTC as timestamp
        mock_datetime.now.return_value = mock_now_instance
        mock_datetime.now.return_value.isoformat.return_value = "2023-01-01T12:00:00+00:00"
        
        # Test data
        submission_id = "test-submission-123"
        body = {
            "submission_id": submission_id,
            "type": "listing"
        }
        
        # Call the function
        lambda_module.log_failure_to_dynamodb(submission_id, body)
        
        # Check if DynamoDB put_item was called with correct parameters
        mock_table.put_item.assert_called_once()
        call_args = mock_table.put_item.call_args[1]['Item']
        
        # Verify the data
        assert call_args['RequestID'] == submission_id
        assert call_args['api_type'] == "listing"
        assert call_args['status'] == "INVALID_REQUEST"
        assert json.loads(call_args['payload'])['submission_id'] == submission_id
        
        # We don't check exact timestamp because we're mocking datetime differently now
        assert 'timestamp' in call_args
        assert 'ttl' in call_args

def test_write_json_to_s3(lambda_module):
    """Test write_json_to_s3 function"""
    with patch.object(lambda_module, 's3_client') as mock_s3, \
         patch.object(lambda_module, 'S3_BUCKET_NAME', 'test-bucket'):
        
        # Test data
        submission_id = "test-submission-123"
        json_data = {
            "submission_id": submission_id,
            "type": "listing",
            "data": "test-data"
        }
        
        # Call the function
        lambda_module.write_json_to_s3(submission_id, json_data)
        
        # Check if S3 put_object was called with correct parameters
        mock_s3.put_object.assert_called_once_with(
            Bucket='test-bucket',
            Key=f'invalid-requests/{submission_id}.json',
            Body=json.dumps(json_data),
            ContentType='application/json'
        )

def test_send_message_to_notification_queue(lambda_module):
    """Test send_message_to_notification_queue function"""
    with patch.object(lambda_module, 'sqs_client') as mock_sqs, \
         patch.object(lambda_module, 'NOTIFICATION_SUCESS_QUEUE_URL', 'test-queue-url'):
        
        # Test data
        body = {
            "submission_id": "test-submission-123",
            "type": "listing",
            "data": "test-data"
        }
        
        # Call the function
        lambda_module.send_message_to_notification_queue(body)
        
        # Check if SQS send_message was called with correct parameters
        mock_sqs.send_message.assert_called_once_with(
            QueueUrl='test-queue-url',
            MessageBody=json.dumps(body)
        )

def test_lambda_handler(lambda_module):
    """Test lambda_handler function"""
    with patch.multiple(lambda_module, 
                        log_failure_to_dynamodb=DEFAULT, 
                        write_json_to_s3=DEFAULT, 
                        send_message_to_notification_queue=DEFAULT) as mocks:
        
        # Get the mocks
        mock_log = mocks['log_failure_to_dynamodb']
        mock_write = mocks['write_json_to_s3']
        mock_send = mocks['send_message_to_notification_queue']
        
        # Test event with SQS record
        event = {
            "Records": [
                {
                    "messageId": "test-message-id",
                    "body": json.dumps({
                        "submission_id": "test-submission-123",
                        "type": "listing",
                        "data": "test-data"
                    })
                }
            ]
        }
        
        # Call the handler
        response = lambda_module.lambda_handler(event, {})
        
        # Check if all functions were called
        mock_log.assert_called_once()
        mock_write.assert_called_once()
        mock_send.assert_called_once()
        
        # Check the response
        assert response['statusCode'] == 200
        assert json.loads(response['body'])['message'] == "Processed all failed requests successfully"