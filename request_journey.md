# Image Processing Request Journey

```mermaid
sequenceDiagram
    participant <PERSON><PERSON> as CSV File
    participant Script as process_images_sqs.py
    participant SQS as AWS SQS Queue
    participant Processor as Image Processor
    participant DB as Results Database

    CSV->>Script: Read image URLs
    Note over Script: Group images into batches
    Note over Script: Generate unique submission_id
    Note over Script: Create payload with:<br/>- customer_type: "Ruby"<br/>- type: "IMAGE_VALIDATION"<br/>- listing_id<br/>- country_code<br/>- submission_id<br/>- images<br/>- custom_input_list
    
    Script->>Script: Validate JSON format
    Script->>SQS: Send message with payload
    SQS-->>Script: Return message ID
    
    Note over Script: Store results in CSV:<br/>- batch_number<br/>- property_id<br/>- submission_id<br/>- image_url<br/>- image_index<br/>- sqs_message_id
    
    SQS->>Processor: Trigger processing
    Note over Processor: Validate images
    Note over Processor: Classify images
    Note over Processor: Calculate quality scores
    
    Processor->>DB: Store processing results
    
    Note over Processor: If submission_id missing:<br/>ERROR: Missing submission_id
    Note over Processor: If J<PERSON><PERSON> invalid:<br/>ERROR: Invalid JSON format
```

## Process Flow Description

1. **Input**: The process starts with a CSV file containing image URLs.

2. **Batch Processing**: The `process_images_sqs.py` script reads the CSV and groups images into batches.

3. **Payload Creation**: For each batch:
   - A unique `submission_id` is generated
   - A payload is created with required fields including `customer_type`, `type`, `listing_id`, etc.
   - The JSON payload is validated to ensure proper formatting

4. **SQS Queue**: The payload is sent to an AWS SQS queue, which returns a message ID.

5. **Result Tracking**: Information about each processed image is stored in a results CSV file.

6. **Image Processing**: The SQS message triggers the image processor, which:
   - Validates the images
   - Classifies them according to the custom input list
   - Calculates quality scores

7. **Error Handling**:
   - If `submission_id` is missing: "Missing submission_id" error
   - If JSON format is invalid: "Invalid JSON format" error

8. **Results Storage**: Processing results are stored in a database for later retrieval.
