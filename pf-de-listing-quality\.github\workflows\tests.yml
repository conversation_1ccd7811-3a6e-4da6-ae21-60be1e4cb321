name: Run-Tests

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ develop ]
  # Optional: Allow manual triggering from the Actions tab
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        cd ./app/lambdas/listing_quality/
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f requirements-test.txt ]; then pip install -r requirements-test.txt; fi

    - name: Run listing_quality lambda tests
      run: |
        cd app/lambdas/listing_quality
        export AWS_DEFAULT_REGION=us-east-1 
        coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v tests/ && coverage report -m -i

    - name: Run dispatcher_request_queuing lambda tests
      run: |
        cd app/lambdas/dispatcher_request_queuing
        export AWS_DEFAULT_REGION=us-east-1 
        coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v tests/ && coverage report -m -i

    - name: Run invalid_request_handler lambda tests
      run: |
        cd app/lambdas/invalid_request_handler
        export AWS_DEFAULT_REGION=us-east-1 
        coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v tests/ && coverage report -m -i

    - name: Run report_failed_requests lambda tests
      run: |
        cd app/lambdas/report_failed_requests
        export AWS_DEFAULT_REGION=us-east-1 
        coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v tests/ && coverage report -m -i

    - name: Run request_invocation lambda tests
      run: |
        cd app/lambdas/request_invocation
        export AWS_DEFAULT_REGION=us-east-1 
        coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v tests/ && coverage report -m -i

    - name: Run request_queuing lambda tests
      run: |
        cd app/lambdas/request_queuing
        export AWS_DEFAULT_REGION=us-east-1 
        coverage run --source "${PWD}" --omit "**/tests/*" -m pytest -v tests/ && coverage report -m -i
        