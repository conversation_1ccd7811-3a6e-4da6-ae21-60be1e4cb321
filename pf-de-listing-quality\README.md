# Listing Quality Service

The Listing Quality Service is an AWS-based system that processes listing quality assessments using AWS Lambda functions and SQS queues for distributed processing, prioritization, and reliability.

## Architecture Overview

The service uses a serverless architecture built with AWS CDK with the following components:

- **Request Queuing System**: Categorizes requests into different SLA tiers (Diamond, Emerald, Ruby, Sapphire)
- **Processing Pipeline**: Manages concurrent processing of requests based on priority
- **AI Integration**: Uses Amazon Bedrock with apac.apac.amazon.nova-pro-v1:0:0 for quality assessments
- **Monitoring & Error Handling**: Tracks request lifecycles and reports failures

## Infrastructure

The infrastructure is defined using AWS CDK with TypeScript and consists of:

- Lambda functions for request processing
- SQS queues for prioritization and buffering
- EventBridge rules for scheduling
- CloudWatch for monitoring
- DynamoDB for tracking request state
- Parameter Store for configuration
- ECR for container registry

## GitHub Actions Workflows

### 1. Build-PR

Builds and pushes Docker images when a PR comment contains `.build`.

```yaml
# Triggered by PR comments containing '.build'
# Builds and pushes Docker images to ECR
# Updates the Lambda function with the latest image
```

### 2. Upload-To-S3

Uploads configuration and prompt files to S3 when a PR comment contains `.upload`.

```yaml
# Triggered by PR comments containing '.upload'
# Uploads config files to S3 bucket
# Uploads prompt templates to S3 bucket
# Verifies the upload completion
```

### 3. Deploy-PR

Deploys the AWS CDK stack when triggered through branch-deploy comments.

```yaml
# Triggered by PR comments containing '.deploy'
# Uses GitHub's branch-deploy for deployment management
# Runs CDK deployment to update AWS resources
# Sends Slack notifications on completion
```

## Project Structure

```
.
├── app/
│   ├── config/           # Configuration files uploaded to S3
│   ├── listing_quality/  # Docker container code for the quality assessment Lambda
│   └── prompts/          # Prompt templates uploaded to S3
├── src/
│   ├── dispatcher/       # Lambda code for dispatching requests based on priority
│   ├── report_failed_requests/ # Lambda code for handling failed requests
│   ├── request_invocation/ # Lambda code for invoking the quality assessment
│   └── request_queuing/  # Lambda code for queuing incoming requests
├── lib/
│   ├── listing-quality-lambda-stack.ts # CDK stack definition
│   └── parameters.ts     # SSM Parameter Store configuration
└── cdk.json             # CDK configuration
```

## Key Features

- **Priority-based Processing**: Uses different SLA tiers (Diamond, Emerald, Ruby, Sapphire) with configurable processing ratios
- **Scalable Architecture**: Leverages serverless components for automatic scaling
- **Robust Error Handling**: DLQ implementation with failed request reporting
- **Configurable AI Integration**: Uses parameter store for model configuration
- **Resource Efficiency**: Batch processing and concurrent workers

## Deployment

The project uses GitHub Actions for its CI/CD pipeline:

1. **PR Comments**:
   - `.build`: Build and push Docker image to ECR
   - `.upload`: Upload config and prompts to S3
   - `.deploy`: Deploy the CDK stack

2. **Environments**:
   - The code is configured for a `staging` environment

## Configuration

Configuration is managed through AWS SSM Parameter Store with parameters defined in `parameters.ts`:

- Bedrock model configuration
- S3 bucket locations for artifacts and tracking
- SQS queue URLs
- DynamoDB table settings
- Image processing limits

## Development

To develop and deploy locally:

1. Set up AWS credentials
2. Install dependencies: `npm install`
3. Build the project: `npm run build`
4. Deploy the stack: `cdk deploy`

## Monitoring and Logging

- CloudWatch Logs are configured for Bedrock invocations
- Request tracking is stored in DynamoDB with configurable TTL
- Failed requests are reported and stored in S3