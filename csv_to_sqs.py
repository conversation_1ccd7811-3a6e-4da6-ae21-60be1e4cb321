import argparse
import csv
import json
import re
from typing import List, Dict, Any
import boto3

QUEUE_NAME_DEFAULT = "listing-quality-gen-ai-request-queue-staging"
REGION_DEFAULT = "ap-southeast-1"

def parse_image_list(cell: str) -> List[str]:
    """Parse a cell like "[url1, url2, ...]" into a list of URLs."""
    if not isinstance(cell, str):
        return []
    s = cell.strip()
    if s.startswith("[") and s.endswith("]"):
        s = s[1:-1]
    # Split by comma but keep URLs intact; then strip whitespace
    parts = [p.strip() for p in s.split(",")]
    # Filter out empty entries
    urls = [p for p in parts if p]
    return urls

def create_payload(listing_id: int, image_url: str, country_code: str = "ae") -> Dict[str, Any]:
    images = [{"id": 1, "src": image_url}]
    return {
        "customer_type": "Ruby",
        "type": "IMAGE_VALIDATION",
        "listing_id": listing_id,
        "country_code": country_code.lower(),
        "submission_id": str(listing_id),
        "validate_params": [],
        "images": images,
        "custom_input_list": {},
    }

def get_queue_url(sqs_client, queue_name: str) -> str:
    resp = sqs_client.get_queue_url(QueueName=queue_name)
    return resp["QueueUrl"]

def send_payload(sqs_client, queue_url: str, payload: Dict[str, Any]) -> None:
    body = json.dumps(payload)
    # Validate JSON
    json.loads(body)
    resp = sqs_client.send_message(QueueUrl=queue_url, MessageBody=body)
    print(f"Sent listing_id={payload['listing_id']} MessageId={resp.get('MessageId')}")

def read_rows(path: str) -> List[Dict[str, Any]]:
    """
    Reads a two-column CSV with structure:
    <listing_id>,<image>
    Returns list of dicts with listing_id and image URL.
    """
    rows: List[Dict[str, Any]] = []

    with open(path, "r", encoding="utf-8-sig", newline="") as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row["listing_id"] and row["image"]:
                try:
                    listing_id = int(row["listing_id"].strip())
                    image_url = row["image"].strip()
                    rows.append({"listing_id": listing_id, "image": image_url})
                except ValueError:
                    continue
    return rows

def main():
    parser = argparse.ArgumentParser(description="Read CSV of listing_id and image URLs, send payloads to SQS.")
    parser.add_argument("--csv", default="last_results.csv", help="Path to the CSV file (e.g., C:\\Users\\<USER>\\Downloads\\2025-09-10 9_30am.csv)")
    parser.add_argument("--queue-name", default=QUEUE_NAME_DEFAULT, help="SQS queue name")
    parser.add_argument("--region", default=REGION_DEFAULT, help="AWS region")
    parser.add_argument("--country", default="ae", help="Country code, e.g., ae")
    args = parser.parse_args()

    rows = read_rows(args.csv)
    if not rows:
        print("No valid rows found. Ensure the file has: listing_id,image")
        return

    sqs = boto3.client("sqs", region_name=args.region)
    queue_url = get_queue_url(sqs, args.queue_name)

    print(f"Sending {len(rows)} listings to {args.queue_name} in {args.region}...")
    for r in rows:
        payload = create_payload(r["listing_id"], r["image"], args.country)
        print(json.dumps(payload,indent=2))
        send_payload(sqs, queue_url, payload)

if __name__ == "__main__":
    main()