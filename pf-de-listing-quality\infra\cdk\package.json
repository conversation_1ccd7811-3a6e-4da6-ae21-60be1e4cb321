{"name": "listing-quality-staging-lambda-cdk", "version": "1.0.0", "description": "CDK for Listing Quality Lambda Functions", "scripts": {"build": "tsc", "cdk": "npx cdk", "deploy": "npx aws-cdk deploy --require-approval never", "test": "echo \"No tests specified\" && exit 0"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"aws-lambda": "^1.0.7"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/node": "^20.4.5", "aws-cdk-lib": "2.175.1", "constructs": "^10.2.69", "esbuild": "^0.18.17", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}