# Image Processing APIs

This repository contains a set of API endpoints designed for image processing tasks, leveraging apac.apac.amazon.nova-pro-v1:0:0. The APIs include functionality for Image Detail Extraction, Image Quality Validation, Description Validation, and Image Enhancement.

## Table of Contents
- [Overview](#overview)
- [API Endpoints](#api-endpoints)
- [Technologies Used](#technologies-used)
- [Environment Setup](#environment-setup)
- [Running the Project Locally](#running-the-project-locally)
  - [Using Uvicorn](#using-uvicorn)
  - [Using Docker](#using-docker)
- [Accessing the API Documentation](#accessing-the-api-documentation)
- [Contributing](#contributing)
- [License](#license)

## Overview

This project provides four main API endpoints:
2. **Image Quality Validation** - Validates the quality of images based on predefined metrics.

These endpoints utilize apac.apac.amazon.nova-pro-v1:0:0 for processing and generating results, ensuring robust and accurate outputs.

## API Endpoints

- **Image Quality Validation**: `/validate_quality`

Each endpoint accepts specific parameters and returns detailed responses, enhancing the image processing workflow.

## Technologies Used

- **Python**
- **FastAPI**
- **apac.apac.amazon.nova-pro-v1:0:0**
- **AWS (for cloud storage and processing)**
- **Docker (for containerization)**

## Environment Setup

To run this project locally, ensure you have the following environment variables set up in your `.env` file:

```env
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS
COMFY_UI_SERVER_ADDRESS=YOUR_EC2_INSTANCE_PUBLIC_IP_ADDRESS
CONCURRENT_WORKERS=10
```

Replace placeholders with your actual credentials and server addresses.

## Running the Project Locally

You can run the project locally using two methods:

### Using Uvicorn

1. **Create a Python virtual environment:**
   ```bash
   python -m venv venv
   ```
   
2. **Activate the virtual environment:**
   - On Windows:
     ```bash
     .\venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```bash
     source venv/bin/activate
     ```

3. **Install the required dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Navigate to the main project directory where the `main.py` file is located.**

5. **Run the application using Uvicorn:**
   ```bash
   uvicorn main:app --reload
   ```

### Using Docker

1. **Install Docker on your system** if it's not already installed. Follow the official [Docker installation guide](https://docs.docker.com/get-docker/).

2. **Rename `dockerfile-local` to `Dockerfile`.**

3. **Build the Docker image:**
   ```bash
   docker build -t <docker-image-name> .
   ```
   Replace `<docker-image-name>` with your preferred name for the Docker image.

4. **Run the Docker container:**
   ```bash
   docker run -d -p 8000:8000 <docker-image-name>:latest
   ```

5. After running the above command, you should see the container running in your Docker GUI.

### Using SAM CLI

1. **Install SAM CLI from [link](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)**
2. Run `sam build`
3. Run `sam local invoke PFFastAPIFunction --event event.json`

## Accessing the API Documentation

Once the server is running, you can access the API documentation through Swagger UI by navigating to:

```
http://127.0.0.1:8000/docs
```

This provides a detailed interface to explore and test the available endpoints.

### PyTest Testing Flow

1. **Create and Active a Virtual Environment**
   ```bash
      python -m venv venv
      source venv/bin/activate
   ```
2. **Install Dependencies**
   ```bash
      pip install -r requirements.txt
   ```

3. **Set Up Test Environment Variables**
Create a .env.test file in the project root with necessary app configurations:
   ```bash
      ENV=test
      LOG_LEVEL=INFO
      MOCK_LLM_RESPONSE=False
      LLM_MODEL=bedrock-2023-05-31
      AWS_DEFAULT_REGION=us-east-1
      AWS_ACCESS_KEY_ID_TEST=
      AWS_SECRET_ACCESS_KEY_TEST=
      AWS_SESSION_TOKEN_TEST=
   ```
4. **Run the Tests**
   ```bash
      pytest --cov=. --cov-report=term-missing --cov-report=html --cov-config=.coveragerc tests/
   ```
