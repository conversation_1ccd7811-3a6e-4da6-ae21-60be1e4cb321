import json
import logging
import random
import time
import uuid
import os
import boto3
from typing import List, Dict, Any

# =============================================
# Configuration Constants (with env var overrides)
# =============================================

# AWS Configuration
DEFAULT_AWS_REGION = os.getenv("AWS_DEFAULT_REGION", "ap-southeast-1")
# SQS Configuration
SQS_QUEUE_URL = os.getenv("SQS_QUEUE_URL",
                          "https://sqs.ap-southeast-1.amazonaws.com/************/listing-quality-gen-ai-request-queue-staging")  # No default, must be provided

# Load Test Defaults
DIAMOND_COUNT = int(os.getenv("DIAMOND_COUNT", "10"))
EMERALD_COUNT = int(os.getenv("EMERALD_COUNT", "10"))
RUBY_COUNT = int(os.getenv("RUBY_COUNT", "10"))
SAPPHIRE_COUNT = int(os.getenv("SAPPHIRE_COUNT", "10"))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "4"))
BATCH_DELAY = float(os.getenv("BATCH_DELAY", "0"))
# DEFAULT_ITERATIONS = float(os.getenv("ITERATIONS", "1"))

# Customer types
CUSTOMER_TYPES = ["diamond", "emerald", "ruby", "sapphire"]

# =============================================
# Payload Template
# =============================================

PAYLOAD_TEMPLATE = {
    "submission_id": "",
    "customer_type": "",
    "type": "IMAGE_VALIDATION",
    "listing_id": 43345345,
    "country_code": "AE",
    "validate_params": [],
    "images": [
        {
            "id": 0,
            "src": "https://images.bayut.com/thumbnails/741712964-1066x800.webp"
        },
        {
            "id": 1,
            "src": "https://www.propertyfinder.ae/property/b5121ab9d6fcf51530964f9985fd43dd/1312/894/MODE/007e04/13734243-0dbe5o.webp?ctr=ae"
        },
        {
            "id": 2,
            "src": "https://pf-graph-images-production.s3.ap-southeast-1.amazonaws.com/ae/community/86/561143eb251f4a7a27ac35184720006c/watermark.jpeg"
        },
        {
            "id": 3,
            "src": "https://www.propertyfinder.ae/property/5fe34176a9ef96f2f4c6c322133e3db8/1312/894/MODE/47c9a8/13655079-c1d4eo.webp?ctr=ae"
        },
        {
            "id": 4,
            "src": "https://www.propertyfinder.ae/property/17e3bf0f1a2ba1d02e9bc23f1b7971dc/1312/894/MODE/ac9c45/13710124-b44a1o.webp?ctr=ae"
        },
        {
            "id": 5,
            "src": "https://pf-graph-images-production.s3.ap-southeast-1.amazonaws.com/ae/community/290/a7f56530738ad0e1904d96ba9fdcd7a7/watermark.jpeg"
        },
        {
            "id": 6,
            "src": "https://www.propertyfinder.ae/property/caec67ba3374e7797dd6669546b17f86/1312/894/MODE/f5226f/13711472-7652fo.webp?ctr=ae"
        },
        {
            "id": 7,
            "src": "https://www.propertyfinder.ae/property/b33c36b6201a1f432e6cfa378e3c44e1/1312/894/MODE/c746c2/13710124-c54dao.webp?ctr=ae"
        },
        {
            "id": 8,
            "src": "https://www.propertyfinder.ae/property/c9bd5aefcb568f9ed29d9fbe0761b1ce/1312/894/MODE/46d6ae/13707546-33184o.webp?ctr=ae"
        },
        {
            "id": 9,
            "src": "https://www.propertyfinder.ae/property/223bf5e7cc9f2cc66e180bbf9786095f/1312/894/MODE/2bea02/13706198-fb887o.webp?ctr=ae"
        },
        {
            "id": 10,
            "src": "https://www.propertyfinder.ae/property/b7c9c0028d668a43cbfd6a1441b22b37/1312/894/MODE/5e4a58/13698133-85c36o.webp?ctr=ae"
        },
        {
            "id": 11,
            "src": "https://pf-graph-images-production.s3.ap-southeast-1.amazonaws.com/ae/building/12103/111327bd599f531e04313117d73c398c/watermark.jpeg"
        },
        {
            "id": 12,
            "src": "https://www.propertyfinder.ae/property/cbbb930bd29641f0cb60f8af0f3f2379/1312/894/MODE/9bb418/13690670-79a58o.webp?ctr=ae"
        },
        {
            "id": 13,
            "src": "https://www.propertyfinder.ae/property/d8799a13b7fb55501d0eca879fd2f827/1312/894/MODE/45f3da/13692747-a823co.jpg?ctr=ae"
        },
        {
            "id": 14,
            "src": "https://pf-graph-images-production.s3.ap-southeast-1.amazonaws.com/ae/building/743/3571a46b7807e03a4007a7141cc2ff19/watermark.jpeg"
        }
    ],
    "custom_input_list": {
        "photo_classification": [
            "Open kitchen", "Closed kitchen", "dining room", "bedroom",
            "bathroom", "brochure", "floor plan", "map", "logo",
            "living room", "garden", "balcony", "building exterior",
            "parking", "MaidRoom", "laundry", "Corridor", "multiple_images"
        ],
        "room_category": ["Luxury", "Modern", "Classic"],
        "room_size": ["small", "medium", "large"]
    }
}


# =============================================
# Core Functions
# =============================================

def generate_payload(customer_type: str) -> Dict[str, Any]:
    """Generate a payload with unique submission_id and specified customer_type"""
    payload = json.loads(json.dumps(PAYLOAD_TEMPLATE))  # Deep copy
    payload["submission_id"] = f"loadtest-{str(uuid.uuid4())}"
    payload["customer_type"] = customer_type
    return payload


def create_sqs_client() -> boto3.client:
    print(f"Creating SQS client {DEFAULT_AWS_REGION}")
    """Create SQS client with credentials from environment"""
    return boto3.client(
        'sqs',
        region_name=DEFAULT_AWS_REGION,
    )


def send_messages_to_sqs(
        sqs_client,
        queue_url: str,
        messages: List[Dict[str, Any]],
        batch_size: int = 10
) -> None:
    """Send messages to SQS in batches"""
    for i in range(0, len(messages), batch_size):
        batch = messages[i:i + batch_size]
        entries = [
            {
                'Id': str(idx),
                'MessageBody': json.dumps(message),
            }
            for idx, message in enumerate(batch)
        ]

        print(f"Sending {len(entries)} messages to SQS")
        for message in batch:
            print(f'Sending submission_id: {message.get("submission_id")} customer_type: {message.get("customer_type")} messages to SQS')

        try:
            response = sqs_client.send_message_batch(
                QueueUrl=queue_url,
                Entries=entries
            )

            if 'Failed' in response and len(response['Failed']) > 0:
                print(f"Failed to send {len(response['Failed'])} messages")
            else:
                print(f"Successfully sent batch of {len(batch)} messages")
        except Exception as e:
            print(f"Error sending batch: {str(e)}")
            raise


def load_test(
        sqs_client,
        queue_url: str,
        diamond_count: int,
        emerald_count: int,
        ruby_count: int,
        sapphire_count: int,
        batch_size: int,
        batch_delay: float,
        # iterations: int,
) -> None:
    """Main load testing function"""
    # Generate all messages first
    messages = []

    print("Generating messages...")
    for _ in range(diamond_count):
        messages.append(generate_payload("diamond"))
    for _ in range(emerald_count):
        messages.append(generate_payload("emerald"))
    for _ in range(ruby_count):
        messages.append(generate_payload("ruby"))
    for _ in range(sapphire_count):
        messages.append(generate_payload("sapphire"))

    random.shuffle(messages)  # Mix the customer types

    print("\nLoad Test Configuration:")
    print(f"Total messages to send: {len(messages)}")
    print(f"  Diamond: {diamond_count}")
    print(f"  Emerald: {emerald_count}")
    print(f"  Ruby: {ruby_count}")
    print(f"  Sapphire: {sapphire_count}")
    print(f"Batch size: {batch_size}")
    print(f"Delay between batches: {batch_delay} seconds\n")

    # Send messages in batches with delay
    total_sent = 0
    start_time = time.time()

    try:
        for i in range(0, len(messages), batch_size):
            batch = messages[i:i + batch_size]
            send_messages_to_sqs(sqs_client, queue_url, batch)
            total_sent += len(batch)
            print(f"Progress: {total_sent}/{len(messages)} ({total_sent / len(messages) * 100:.1f}%)")

            if i + batch_size < len(messages) and batch_delay > 0:
                print(f"Waiting {batch_delay} seconds before next batch...")
                time.sleep(batch_delay)
    except KeyboardInterrupt:
        print("\nLoad test interrupted by user")
    finally:
        elapsed = time.time() - start_time
        print(f"\nLoad test completed! Sent {total_sent} messages in {elapsed:.2f} seconds")


# =============================================
# Main Function
# =============================================

def main():
    # Initialize SQS client
    try:
        sqs_client = create_sqs_client()
        # Test the connection
        sqs_client.get_queue_attributes(
            QueueUrl=SQS_QUEUE_URL,
            AttributeNames=['QueueArn']
        )
    except Exception as e:
        logging.exception(e)
        print(f"Error connecting to SQS: {str(e)}")
        print("Please check your AWS credentials and queue URL")
        return

    # Start load test
    load_test(
        sqs_client=sqs_client,
        queue_url=SQS_QUEUE_URL,
        diamond_count=DIAMOND_COUNT,
        emerald_count=EMERALD_COUNT,
        ruby_count=RUBY_COUNT,
        sapphire_count=SAPPHIRE_COUNT,
        batch_size=BATCH_SIZE,
        batch_delay=BATCH_DELAY,
        # iterations=DEFAULT_ITERATIONS
    )


if __name__ == "__main__":
    main()
