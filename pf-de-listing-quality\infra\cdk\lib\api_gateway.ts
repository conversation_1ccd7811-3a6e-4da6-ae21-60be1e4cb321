import {Construct} from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';

export interface QualityApiGatewayProps {
  qualityResponseLambda: lambda.IFunction;
  stage: string;
}

/**
 * Sets up the API Gateway for the Quality Response endpoint, integrating with the qualityResponse Lambda.
 * Exposes GET /quality-response/{id} for internal use.
 */
export class QualityApiGateway extends Construct {
  public readonly api: apigateway.RestApi;

  constructor(scope: Construct, id: string, props: QualityApiGatewayProps) {
    super(scope, id);

    // Create the API Gateway REST API
    this.api = new apigateway.RestApi(this, 'QualityResponseApi', {
      restApiName: `QualityResponseApi-${props.stage}`,
      deployOptions: {
        stageName: props.stage
      },
      endpointTypes: [apigateway.EndpointType.REGIONAL],
    });

    // Define the /quality-response/{id} endpoint
    const qualityResponse = this.api.root.addResource('quality-response');
    qualityResponse.addCorsPreflight({
      allowOrigins: ['*'],
      allowMethods: ['GET', 'OPTIONS'],
      allowHeaders: ['Content-Type', 'X-Amz-Date', 'Authorization', 'X-Api-Key']
    });

    const idResource = qualityResponse.addResource('{id}');

    // Integrate GET with Lambda
    idResource.addMethod('GET', new apigateway.LambdaIntegration(props.qualityResponseLambda, {
      proxy: true
    }), {
      apiKeyRequired: true
    });

    const apiKey = new apigateway.ApiKey(this, 'QualityApiKey', {
      apiKeyName: `QualityApiKey-${props.stage}`,
      description: 'API Key for Quality API access',
      enabled: true
    });

    const usagePlan = new apigateway.UsagePlan(this, 'QualityApiUsagePlan', {
      name: `QualityApiUsagePlan-${props.stage}`,
      throttle: {
        rateLimit: 100,
        burstLimit: 200
      }
    });

    usagePlan.addApiStage({
      stage: this.api.deploymentStage
    });

    usagePlan.addApiKey(apiKey);
  }
}
