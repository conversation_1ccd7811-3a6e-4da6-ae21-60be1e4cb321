import json
import os
import asyncio
import boto3

from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_warning,
    log_frame_error,
    log_frame_debug,
)
from config.config import config


# Initialize the configuration object
config = config.config

# Initialize the S3 client to interact with AWS S3
s3_client = boto3.client("s3")

# Set up the logger for logging events and errors
logger = get_logger(__name__)


# Class to handle loading and managing prompt data
class PromptLoader:
    _prompts = {}  # Class-level dictionary to store prompt names and filenames
    _prompt_contents = {}  # Class-level dictionary to store prompt contents
    _s3_client = None  # Class-level variable for S3 client

    # Class method to load prompts from a JSON file and download them from S3
    @classmethod
    async def load_prompts(cls, prompts_file_path=None):
        """Loads all prompts from a JSON file into memory and downloads them from S3."""
        try:
            # If no file path is provided, default to loading from 'prompts.json'
            if prompts_file_path is None:
                prompts_file_path = os.path.join(
                    os.path.dirname(__file__), "prompts.json"
                )

            # Convert relative path to absolute path
            prompts_file_path = os.path.abspath(prompts_file_path)

            # Open and read the JSON file containing the prompts
            with open(prompts_file_path, "r") as file:
                cls._prompts = json.load(file)

            log_frame_info(
                logger,
                message="Prompts JSON file loaded successfully from",
                prompts_file_path=prompts_file_path,
            )

            # Fetch all prompt files from S3 and cache them in memory
            await cls._download_all_prompts()

        # Handle errors related to file loading or JSON decoding
        except (FileNotFoundError, json.JSONDecodeError) as e:
            log_frame_error(logger, message="Error loading prompts.json", error=str(e))
            cls._prompts = {}  # Ensure the prompts dictionary is empty if an error occurs

    # Class method to initialize the S3 client if it is not already initialized
    @classmethod
    def _initialize_s3_client(cls):
        """Initializes the S3 client if not already initialized."""
        if cls._s3_client is None:
            cls._s3_client = boto3.client("s3")

    # Class method to download a single prompt from S3 and store it in memory
    @classmethod
    def _download_single_prompt(cls, bucket_name, prompt_name, prompt_file_name):
        try:
            # Define the S3 key for the prompt file
            prompt_file_key = f"prompts/{prompt_file_name}"

            # Fetch the prompt file from S3
            response = cls._s3_client.get_object(
                Bucket=bucket_name, Key=prompt_file_key
            )

            # Read the file content, decode, and store it in memory
            body = response["Body"].read().decode("utf-8").strip()
            cls._prompt_contents[prompt_name] = body

            log_frame_debug(
                logger,
                message="Prompt downloaded and cached from S3.",
                prompt_name=prompt_name,
            )
        except Exception as e:
            # Log error if there is an issue with downloading the prompt from S3
            log_frame_error(
                logger,
                message="Error downloading prompt from S3",
                prompt_name=prompt_name,
                error=str(e),
                exc_info=True,
            )

    # Class method to download all prompts from S3 and cache them in memory
    @classmethod
    async def _download_all_prompts(cls):
        """Downloads all prompt files from S3 and caches them in memory."""

        # Retrieve the S3 bucket name from the configuration
        bucket_name = config.get(
            "s3_bucket_artifacts", None
        )  # Default to None if missing

        # If the S3 bucket name is missing, log an error and return
        if not bucket_name:
            log_frame_error(
                logger, message="S3 bucket name is missing in configuration!"
            )
            return

        # Ensure the S3 client is initialized
        cls._initialize_s3_client()

        # Create a list of asynchronous tasks to download each prompt
        tasks = []
        for prompt_name, prompt_file_name in cls._prompts.items():
            # Skip prompts that have already been downloaded
            if prompt_name in cls._prompt_contents:
                continue

            # Add download tasks to the list
            tasks.append(
                asyncio.to_thread(
                    cls._download_single_prompt,
                    bucket_name,
                    prompt_name,
                    prompt_file_name,
                )
            )

        # Wait for all tasks to complete (download all prompts)
        await asyncio.gather(*tasks)

    # Class method to fetch the content of a specific prompt from memory
    @classmethod
    def get_prompt(cls, prompt_name):
        """Fetches the prompt content from memory."""
        # If the prompt contents are empty, log a warning
        if not cls._prompt_contents:
            log_frame_warning(
                logger,
                message="Prompt contents are empty. Ensure prompts are loaded at startup.",
            )
            return "Error: Prompts not loaded."

        # Return the requested prompt's content, or an error message if not found
        return cls._prompt_contents.get(
            prompt_name, f"Error: Prompt '{prompt_name}' not found."
        )
