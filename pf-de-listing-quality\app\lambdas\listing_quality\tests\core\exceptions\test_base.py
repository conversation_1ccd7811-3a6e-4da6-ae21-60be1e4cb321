import pytest
from http import HTTPStatus

from core.exceptions.base import (
    BaseException,
    BadRequestException,
    NotFoundException,
    ForbiddenException,
    UnauthorizedException,
    UnprocessableEntity,
    ValueDuplicationException,
    DatabaseOperationException,
)  # Adjust import path based on your project structure


def test_base_exception_defaults():
    exc = BaseException()
    assert exc.code == HTTPStatus.BAD_GATEWAY
    assert exc.error_code == HTTPStatus.BAD_GATEWAY
    assert exc.message == HTTPStatus.BAD_GATEWAY.description


def test_base_exception_custom_message():
    custom_msg = "Something custom went wrong"
    exc = BaseException(message=custom_msg)
    assert exc.message == custom_msg


@pytest.mark.parametrize(
    "exception_class,http_status",
    [
        (BadRequestException, HTTPStatus.BAD_REQUEST),
        (NotFoundException, HTTPStatus.NOT_FOUND),
        (ForbiddenException, HTTPStatus.FORBIDDEN),
        (UnauthorizedException, HTTPStatus.UNAUTHORIZED),
        (UnprocessableEntity, HTTPStatus.UNPROCESSABLE_ENTITY),
        (ValueDuplicationException, HTTPStatus.UNPROCESSABLE_ENTITY),
        (DatabaseOperationException, HTTPStatus.INTERNAL_SERVER_ERROR),
    ],
)
def test_derived_exceptions_default_status_and_message(exception_class, http_status):
    exc = exception_class()
    assert exc.code == http_status
    assert exc.error_code == http_status
    assert exc.message == http_status.description


def test_derived_exception_custom_message():
    custom_msg = "Custom error on derived exception"
    exc = BadRequestException(message=custom_msg)
    assert exc.message == custom_msg
