import boto3
import os
import hashlib
import logging
import json

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Get environment variables for downstream Lambda function
LISTING_QUALITY_INVOCATION_LAMBDA = os.getenv("LISTING_QUALITY_INVOCATION_LAMBDA")

# Create boto3 clients for AWS services
sqs_client = boto3.client("sqs")
lambda_client = boto3.client("lambda")

# Priority queue URLs for different customer tiers, retrieved from environment variables
PRIORITY_QUEUES = {
    "diamond": os.environ["DIAMOND_QUEUE_URL"],
    "ruby": os.environ["RUBY_QUEUE_URL"],
    "sapphire": os.environ["SAPPHIRE_QUEUE_URL"],
    "emerald": os.environ["EMERALD_QUEUE_URL"],
}

# Processing queue configuration - destination queue and its capacity
PROCESSING_QUEUE_URL = os.environ["REQUEST_PROCESSING_QUEUE_URL"]
TOTAL_PROCESSING_SIZE = int(os.environ["TOTAL_PROCESSING_SIZE"])

# Ratio of messages to move from each priority queue, determining customer tier prioritization
RATIOS = {
    "diamond": float(os.environ["DIAMOND_RATIO"]),
    "ruby": float(os.environ["RUBY_RATIO"]),
    "sapphire": float(os.environ["SAPPHIRE_RATIO"]),
    "emerald": float(os.environ["EMERALD_RATIO"]),
}


# Function to get the total number of messages in a queue (both available and in-flight)
def get_message_count(queue_url):
    try:
        attribute_names = ["ApproximateNumberOfMessages"]
        if queue_url == PROCESSING_QUEUE_URL:
            attribute_names.append("ApproximateNumberOfMessagesNotVisible")
        response = sqs_client.get_queue_attributes(
            QueueUrl=queue_url,
            AttributeNames=attribute_names
        )
        available = int(response["Attributes"].get("ApproximateNumberOfMessages", 0))
        in_flight = int(
            response["Attributes"].get("ApproximateNumberOfMessagesNotVisible", 0)
        )
        total = available + in_flight
        logger.info(
            f"Queue {queue_url} has {available} available, {in_flight} in flight, total: {total}"
        )
        return total
    except sqs_client.exceptions.RequestThrottled as e:
        logger.error(f"Error fetching message count from {queue_url}: {str(e)}")
        return 0


# Function to fetch a batch of messages from a specified queue
def fetch_messages(queue_url, max_messages):
    messages = []
    try:
        while max_messages > 0:
            # SQS limits batch size to 10, so we may need multiple requests
            num_to_fetch = min(max_messages, 10)
            response = sqs_client.receive_message(
                QueueUrl=queue_url, MaxNumberOfMessages=num_to_fetch, WaitTimeSeconds=1
            )
            if "Messages" not in response:
                break
            messages.extend(response["Messages"])
            max_messages -= len(response["Messages"])
        logger.info(f"Fetched {len(messages)} messages from {queue_url}.")
    except Exception as e:
        logger.error(f"Error fetching messages from {queue_url}: {str(e)}")
    return messages


# Function to send messages to a specified queue, with special handling for FIFO queues
def send_messages(queue_url, messages):
    sent_messages = []
    for message in messages:
        try:
            params = {"QueueUrl": queue_url, "MessageBody": message["Body"]}
            # Add required FIFO queue parameters if the queue is a FIFO queue
            if queue_url.endswith(".fifo"):
                params["MessageGroupId"] = "default-group"
                params["MessageDeduplicationId"] = hashlib.sha256(
                    message["Body"].encode()
                ).hexdigest()
            sqs_client.send_message(**params)
            sent_messages.append(message)
        except Exception as e:
            logger.error(f"Error sending message {message}: {e}")
            continue
    if sent_messages:
        logger.info(f"Sent {len(sent_messages)} messages to {queue_url}.")
    else:
        logger.error(f"Error sending messages to {queue_url}.")
    return sent_messages


# Function to delete processed messages from their source queue
def delete_messages(queue_url, messages):
    try:
        for message in messages:
            sqs_client.delete_message(
                QueueUrl=queue_url, ReceiptHandle=message["ReceiptHandle"]
            )
        logger.info(f"Deleted {len(messages)} messages from {queue_url}.")
    except Exception as e:
        logger.error(f"Error deleting messages from {queue_url}: {str(e)}")


# Algorithm to calculate the optimal distribution of messages based on tier ratios and available capacity
def allocate_messages(priority_counts, empty_space):
    # Initialize allocation with zeros for each queue
    allocated = {queue: 0 for queue in PRIORITY_QUEUES}
    
    # Calculate initial allocation based on ratios
    allocation_counts = {
        queue: max(1, int(empty_space * ratio)) if empty_space * ratio > 0 else 0
        for queue, ratio in RATIOS.items()
    }

    # First pass: allocate based on ratio calculations
    for queue in PRIORITY_QUEUES:
        count = min(priority_counts[queue], allocation_counts[queue])
        allocated[queue] += count
        priority_counts[queue] -= count
        allocation_counts[queue] -= count
    
    # Second pass: distribute any remaining capacity to queues with messages
    leftover = empty_space - sum(allocated.values())
    for queue in PRIORITY_QUEUES:
        if leftover <= 0:
            break
        additional = min(priority_counts[queue], leftover)
        allocated[queue] += additional
        leftover -= additional
    
    return allocated


# Function to move messages from priority queues to the processing queue based on allocation
def move_messages(allocated):
    for queue, count in allocated.items():
        if count == 0:
            continue
        # Fetch messages from priority queue
        messages = fetch_messages(PRIORITY_QUEUES[queue], count)
        if messages:
            # Send messages to processing queue
            sent_messages = send_messages(PROCESSING_QUEUE_URL, messages)
            # Delete successfully sent messages from priority queue
            delete_messages(PRIORITY_QUEUES[queue], sent_messages)


def lambda_handler(event, context):
    try:
        # Get current message counts from all priority queues
        priority_counts = {
            queue: get_message_count(url) for queue, url in PRIORITY_QUEUES.items()
        }
        
        # Early exit if no messages in priority queues
        if sum(priority_counts.values()) == 0:
            logger.info("No messages available in priority queues.")
            return "No messages to move."
        
        # Calculate available capacity in the processing queue
        processing_queue_count = get_message_count(PROCESSING_QUEUE_URL)
        empty_space = TOTAL_PROCESSING_SIZE - processing_queue_count
        logger.info(
            f"Processing queue has {processing_queue_count} messages. {empty_space} slots available."
        )
        
        # Early exit if processing queue is full
        if empty_space <= 0:
            logger.info("Processing queue is full.")
            return "Processing queue is full."
        
        # Run the allocation algorithm to determine message distribution
        allocated = allocate_messages(priority_counts, empty_space)
        
        # Move messages based on the allocation plan
        move_messages(allocated)
        logger.info(f"Messages moved successfully: {allocated}")
        return "Messages moved successfully."
    except Exception as e:
        logger.error(f"Lambda handler encountered an error: {str(e)}")
        return "Error in Lambda execution."
    finally:
        # Trigger the downstream processing lambda asynchronously to handle the moved messages
        lambda_client.invoke(
            FunctionName=LISTING_QUALITY_INVOCATION_LAMBDA,
            InvocationType="Event",
            Payload=json.dumps({}),
        )
        logger.info(f"{LISTING_QUALITY_INVOCATION_LAMBDA} lambda invoked")