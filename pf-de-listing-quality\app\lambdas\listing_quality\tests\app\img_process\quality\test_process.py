import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../")))
import pytest
from io import BytesIO
from PIL import Image
import numpy as np
import pytest
from app.img_process.quality.process import ImageQualityAnalysisModel
from app.img_process.quality.schema import PhotoAttributes
from unittest.mock import patch, Mock, MagicMock
import base64
import requests
from core.enums.status import ResponseStatus

print("Loaded AWS_REGION:", os.getenv("AWS_REGION"))

class MockImageFile:
    def __init__(self, image_bytes):
        self.content = image_bytes

class MockResponse:
    def __init__(self, content):
        self.content = content


@pytest.fixture
def image_quality_model():
    return ImageQualityAnalysisModel()

@pytest.fixture
def model():
    return ImageQualityAnalysisModel()

def test_get_validation_details_valid(model):
    # Manually define all fields needed for the test
    photo_attrs_dict = {
        "brightness": "High",
        "room_type": "Bedroom",  
        "angle": "Wide",         
        "validate_params": [],
        "images": [],
        "custom_input_list": {},
        "listing_id": 123,
        "country_code": "KE",
        "customer_type": None,
        "type": None,
        "submission_id": None,
        "message_handle": None
    }

    processed_params = {
        "brightness": {"name": "brightness", "value": "High"},
        "room_type": {"name": "room_type", "value": "Bedroom"},
        "angle": {"name": "angle", "value": "Wide"},
        "watermark": {"name": "watermark", "value": "Yes"},
    }

    result = model.get_validation_details(photo_attrs_dict, processed_params)
    validation_details, updated_validation_details = result

    assert len(validation_details) == 3  # brightness, room_type, angle
    assert len(updated_validation_details) == 2  # brightness is excluded


def test_get_validation_details_invalid_type(model):
    photo_attrs = ["brightness", "angle"]  # Invalid type
    processed_params = {
        "brightness": {"name": "brightness", "value": "High"}
    }
    result = model.get_validation_details(photo_attrs, processed_params)
    validation_details, updated_validation_details = result

    assert validation_details == []
    assert updated_validation_details == []

def test_get_validation_details_missing_keys(model):
    photo_attrs = {
        "brightness": "High",
        "watermark_text": "Sample Text",  # this key exists in the dict but not in processed_params
        "validate_params": [],
        "images": [],
        "custom_input_list": {},
        "listing_id": 123,
        "country_code": "KE",
        "customer_type": None,
        "type": None,
        "submission_id": None,
        "message_handle": None
    }

    processed_params = {
        "brightness": {"name": "brightness", "value": "High"}
    }
    result = model.get_validation_details(photo_attrs, processed_params)
    validation_details, updated_validation_details = result

    assert len(validation_details) == 1
    # Expect 0 for updated details since the key "brightness" might be excluded in the implementation
    assert len(updated_validation_details) == 0


def create_gray_image_bytes(size=(100, 100), brightness=128):
    # Create a grayscale image with a uniform brightness
    image = Image.new("L", size, color=brightness)
    image_bytes = BytesIO()
    image.save(image_bytes, format="PNG")
    return image_bytes.getvalue()

def test_gray_image_brightness_valid(image_quality_model):
    image_bytes = create_gray_image_bytes(brightness=128)
    im_file = MockImageFile(image_bytes)

    result = image_quality_model.gray_image_brightness(im_file)

    # Expected brightness score at 128 should be ~10
    assert isinstance(result, int)
    assert result == 10  # Because abs(127 - 128)/12.7 = ~0.08 → brightness ≈ 10

def test_gray_image_brightness_invalid_input(image_quality_model):
    im_file = None
    result = image_quality_model.gray_image_brightness(im_file)
    assert result is None

def test_gray_image_brightness_corrupted_image(image_quality_model):
    im_file = MockImageFile(b"not-a-real-image")
    result = image_quality_model.gray_image_brightness(im_file)
    assert result is None

def create_image_bytes(size=(100, 100), color=128):
    # Create a grayscale image with uniform color
    img = Image.new("L", size, color=color)
    buf = BytesIO()
    img.save(buf, format="PNG")
    return buf.getvalue()

def test_calculate_sharpness_valid(image_quality_model):
    image_bytes = create_image_bytes(color=150)
    response = MockResponse(image_bytes)
    
    sharpness = image_quality_model.calculate_sharpness(response)

    assert isinstance(sharpness, float)
    assert sharpness >= 0  # Sharpness can be zero for flat regions

def test_calculate_sharpness_invalid_input(image_quality_model):
    response = None
    result = image_quality_model.calculate_sharpness(response)
    assert result is None

def test_calculate_sharpness_missing_content_attr(image_quality_model):
    class NoContent:
        pass
    result = image_quality_model.calculate_sharpness(NoContent())
    assert result is None

def test_calculate_sharpness_corrupted_image(image_quality_model):
    corrupted_response = MockResponse(b"not-an-image")
    result = image_quality_model.calculate_sharpness(corrupted_response)
    assert result is None

def test_calculate_sharpness_empty_image(image_quality_model):
    # Use a 1x1 grayscale image to simulate "minimal" data (edge case)
    tiny_image = Image.new("L", (1, 1), color=128)
    buf = BytesIO()
    tiny_image.save(buf, format="PNG")
    response = MockResponse(buf.getvalue())
    
    sharpness = image_quality_model.calculate_sharpness(response)

    # The Laplacian variance of a 1x1 image should be 0.0 (no edges)
    assert sharpness == 0.0

def test_get_fuzzy_ratio_exact_match(image_quality_model):
    with patch("app.img_process.quality.process.pre_configs", {"pre_config_competitors_list": ["example"]}):
        detect, matched = image_quality_model.get_fuzzy_ratio("This is an Example watermark.")
        assert detect is True
        assert matched == "example"

def test_get_fuzzy_ratio_fuzzy_match(image_quality_model):
    with patch("app.img_process.quality.process.pre_configs", {"pre_config_competitors_list": ["compete"]}):
        detect, matched = image_quality_model.get_fuzzy_ratio("competes")
        assert detect is True
        assert matched == "compete"

def test_get_fuzzy_ratio_no_match(image_quality_model):
    with patch("app.img_process.quality.process.pre_configs", {"pre_config_competitors_list": ["competitor"]}):
        detect, matched = image_quality_model.get_fuzzy_ratio("something else")
        assert detect is False
        assert matched == ""

def test_get_fuzzy_ratio_invalid_input(image_quality_model):
    with patch("app.img_process.quality.process.pre_configs", {"pre_config_competitors_list": ["test"]}):
        detect, matched = image_quality_model.get_fuzzy_ratio(None)
        assert detect is False
        assert matched == ""

def test_get_fuzzy_ratio_no_config(image_quality_model):
    with patch("app.img_process.quality.process.pre_configs", {}):  # no list
        detect, matched = image_quality_model.get_fuzzy_ratio("example")
        assert detect is False
        assert matched == ""

def test_download_and_encode_image_success(image_quality_model):
    # Simulate a successful image response
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.content = b"image_content"  # Simulate image content

    with patch("requests.get", return_value=mock_response):
        # Assume use_bedrock_invoke is set to "true"
        with patch("app.img_process.quality.process.use_bedrock_invoke", "true"):
            response, encoded_image = image_quality_model.download_and_encode_image("http://example.com/image.jpg")

            # Assert that the response is correct
            assert response.status_code == 200
            assert encoded_image == base64.b64encode(b"image_content").decode("utf-8")

def test_download_and_encode_image_failure(image_quality_model):
    # Simulate a failed HTTP request
    with patch("requests.get", side_effect=requests.exceptions.RequestException("Download failed")):
        response, encoded_image = image_quality_model.download_and_encode_image("http://example.com/image.jpg")

        # Assert that the result is None, None
        assert response is None
        assert encoded_image is None

def test_download_and_encode_image_no_bedrock(image_quality_model):
    # Simulate a successful image response
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.content = b"image_content"  # Simulate image content

    with patch("requests.get", return_value=mock_response):
        # Assume use_bedrock_invoke is set to "false"
        with patch("app.img_process.quality.process.use_bedrock_invoke", "false"):
            response, image = image_quality_model.download_and_encode_image("http://example.com/image.jpg")

            # Assert that the image is not base64 encoded
            assert response.status_code == 200
            assert image == b"image_content"  # raw image content

def test_analyze_image_quality_success(image_quality_model):
    # Mock successful responses for each method
    mock_sharpness = 5.0
    mock_image_quality_factors = {"contrast": 0.8, "saturation": 0.9}
    mock_brightness_score = 7

    with patch.object(image_quality_model, "calculate_sharpness", return_value=mock_sharpness), \
         patch("app.img_process.quality.process.image_quality", return_value=mock_image_quality_factors), \
         patch.object(image_quality_model, "gray_image_brightness", return_value=mock_brightness_score):
        
        # Simulate a successful response (image content)
        mock_response = Mock()
        mock_response.content = b"image_content"
        
        # Call the method
        sharpness_variance, image_quality_factors, brightness_score = image_quality_model.analyze_image_quality(mock_response)
        
        # Assertions to ensure correct results
        assert sharpness_variance == mock_sharpness
        assert image_quality_factors == mock_image_quality_factors
        assert brightness_score == mock_brightness_score

def test_analyze_image_quality_sharpness_failure(image_quality_model):
    # Simulate failure in calculate_sharpness method
    with patch.object(image_quality_model, "calculate_sharpness", return_value=None), \
         patch("app.img_process.quality.process.image_quality", return_value={"contrast": 0.8, "saturation": 0.9}), \
         patch.object(image_quality_model, "gray_image_brightness", return_value=7):
        
        # Simulate a response
        mock_response = Mock()
        mock_response.content = b"image_content"
        
        # Call the method
        sharpness_variance, image_quality_factors, brightness_score = image_quality_model.analyze_image_quality(mock_response)
        
        # Ensure that None is returned for sharpness in case of failure
        assert sharpness_variance is None
        assert image_quality_factors == {"contrast": 0.8, "saturation": 0.9}
        assert brightness_score == 7

def test_analyze_image_quality_image_quality_failure(image_quality_model):
    # Simulate failure in image_quality method
    with patch.object(image_quality_model, "calculate_sharpness", return_value=5.0), \
         patch("app.img_process.quality.process.image_quality", return_value=None), \
         patch.object(image_quality_model, "gray_image_brightness", return_value=7):
        
        # Simulate a response
        mock_response = Mock()
        mock_response.content = b"image_content"
        
        # Call the method
        sharpness_variance, image_quality_factors, brightness_score = image_quality_model.analyze_image_quality(mock_response)
        
        # Ensure that None is returned for image quality factors
        assert sharpness_variance == 5.0
        assert image_quality_factors is None
        assert brightness_score == 7

def test_analyze_image_quality_brightness_failure(image_quality_model):
    # Simulate failure in gray_image_brightness method
    with patch.object(image_quality_model, "calculate_sharpness", return_value=5.0), \
         patch("app.img_process.quality.process.image_quality", return_value={"contrast": 0.8, "saturation": 0.9}), \
         patch.object(image_quality_model, "gray_image_brightness", return_value=None):
        
        # Simulate a response
        mock_response = Mock()
        mock_response.content = b"image_content"
        
        # Call the method
        sharpness_variance, image_quality_factors, brightness_score = image_quality_model.analyze_image_quality(mock_response)
        
        # Ensure that None is returned for brightness in case of failure
        assert sharpness_variance == 5.0
        assert image_quality_factors == {"contrast": 0.8, "saturation": 0.9}
        assert brightness_score is None

def test_should_use_mock_response_true(monkeypatch, image_quality_model):
    monkeypatch.setenv("MOCK_LLM_RESPONSE", "true")
    assert image_quality_model.should_use_mock_response() is True

def test_should_use_mock_response_false(monkeypatch, image_quality_model):
    monkeypatch.setenv("MOCK_LLM_RESPONSE", "false")
    assert image_quality_model.should_use_mock_response() is False

def test_should_use_mock_response_case_insensitive(monkeypatch, image_quality_model):
    monkeypatch.setenv("MOCK_LLM_RESPONSE", "TrUe")
    assert image_quality_model.should_use_mock_response() is True

def test_should_use_mock_response_not_set(monkeypatch, image_quality_model):
    # Ensure it's not set
    monkeypatch.delenv("MOCK_LLM_RESPONSE", raising=False)
    assert image_quality_model.should_use_mock_response() is False

def test_get_photo_classification_prompt_real_s3(model):
    key = "photo_classification"  # This should match the actual key in S3
    value = ["Bedroom", "Kitchen", "Living Room"]

    # Get the prompt using the method
    prompt = model.get_photo_classification_prompt(key, value)

    # Assert that the prompt is a string
    assert isinstance(prompt, str), f"Expected a string, but got {type(prompt)}"

    # Assert that the prompt is not empty
    assert prompt.strip(), "Prompt should not be empty"

    # Optionally, log the prompt for debugging (in real use, we can remove this)
    print(f"Prompt fetched: {prompt}")

def test_get_room_category_prompt_real_s3(model):
    key = "room_category"  # This should match the actual key in S3
    value = ["Bedroom", "Kitchen", "Living Room"]

    # Get the prompt using the method
    prompt = model.get_room_category_prompt(key, value)

    # Assert that the prompt is a string
    assert isinstance(prompt, str), f"Expected a string, but got {type(prompt)}"

    # Assert that the prompt is not empty
    assert prompt.strip(), "Prompt should not be empty"

    # If needed, log the prompt for debugging (can be removed in production)
    print(f"Prompt fetched: {prompt}")


def test_get_new_prompt_real_s3(model):
    image = "dummy_image_url"
    attributes = {"dummy_attr": "value"}
    processed_params = {
        "distortion": "",
        "rotation": "",
        "people_on_photo": "",
        "watermark": "",
        "room_under_construction": "",
        "is_it_a_render": "",
        "is_it_boxed": "",
        "furnished": "",
        "photo_classification": "",
        "room_size": "",
        "room_category": ""
    }
    additional_data = {}

    # Mock the PromptLoader.get_prompt method to simulate S3 access
    with patch("app.img_process.quality.process.PromptLoader.get_prompt") as mock_get_prompt:
        # Set up mock responses for each prompt
        mock_get_prompt.side_effect = lambda key: {
            "picture": "Picture Quality Prompt: {width}, {height}",
            "distortion": "Distortion Prompt",
            "rotation": "Rotation Prompt",
            "people_on_photo": "People on Photo Prompt",
            "watermark": "Watermark Prompt",
            "room_under_construction": "Room Under Construction Prompt",
            "is_it_a_render": "Is It a Render Prompt",
            "is_it_boxed": "Is It Boxed Prompt",
            "furnished": "Furnished Prompt",
            "photo_classification": "Photo Classification Prompt",
            "room_size": "Room Size Prompt",
            "room_category": "Room Category Prompt",
            "main_quality_metrics": "Quality Metrics Prompt: {updated_validation_details}",
            "main_watermark": "Watermark Prompt"
        }.get(key, "")

        # Call the method
        quality_metrics_prompt, watermark_prompt = model.get_new_prompt(image, attributes, processed_params, additional_data)

        # Add debugging print statements to ensure execution
        print(f"Quality Metrics Prompt: {quality_metrics_prompt}")
        print(f"Watermark Prompt: {watermark_prompt}")

    # Check if the mock_get_prompt was called for each key
    expected_keys = [
        "picture", "distortion", "rotation", "people_on_photo", "watermark", 
        "room_under_construction", "is_it_a_render", "is_it_boxed", "furnished"
    ]
    
    # Verify that all necessary prompts were accessed
    for key in expected_keys:
        mock_get_prompt.assert_any_call(key)
        print(f"Mock call for {key} detected.")

    # Validate that the results are properly returned and not empty
    assert isinstance(quality_metrics_prompt, str) and quality_metrics_prompt.strip(), "Quality metrics prompt should be a non-empty string"
    assert isinstance(watermark_prompt, str) and watermark_prompt.strip(), "Watermark prompt should be a non-empty string"

def test_get_watermark_validate_real_s3(model):
    watermark_text = "Sample Watermark"
    image = "dummy_base64_string"  # Assume valid base64 string for image

    # Mock the model controller to return a simulated LLM response
    class DummyModelController:
        def get_model_instance(self, image, prompt):
            return {
                "result": "<output>True</output>",
                "llm_usage": {"tokens_used": 100}
            }

    # Patch internal methods or variables if needed
    model.model_controller = DummyModelController()

    # Force mock mode off to test actual flow
    model.should_use_mock_response = lambda: False

    # Call the function
    result = model.get_watermark_validate(watermark_text, image)

    # Assertions
    assert isinstance(result, dict)
    assert "validated" in result
    assert result["validated"] is True
    assert isinstance(result.get("llm_usage", {}), dict)

    # Optionally print result for debug purposes
    print("Watermark Validate Result:", result)


def test_process_watermark_detection_real_s3(model):
    # Input setup
    image = "dummy_base64_image"
    watermark_prompt = "Base watermark prompt"
    watermark_sample = "SAMPLE WATERMARK TEXT"
    processed_params = {"watermark": True}

    # Mock internal method to simulate LLM response processing
    model.process_llm_response = lambda image, prompt, mock_response: {
        "result": {
            "text": True,
            "watermark_text": "THE BEST",
            "watermark": True
        },
        "llm_usage": {"tokens_used": 50}
    }

    # Mock fuzzy matching and validation
    model.get_fuzzy_ratio = lambda text: (True, text)
    model.get_watermark_validate = lambda text, image: {
        "validated": True,
        "llm_usage": {"tokens_used": 25}
    }

    # Run the function
    result = model.process_watermark_detection(image, processed_params, watermark_prompt, watermark_sample)

    # Assertions
    assert isinstance(result, dict)
    assert "result" in result
    assert "llm_usage" in result
    assert result["result"]["watermark"] is True
    assert result["result"]["watermark_text"] == "THE BEST"
    assert "watermark_detect_llm_usage" in result["llm_usage"]
    assert "watermark_validate_llm_usage" in result["llm_usage"]


def test_process_llm_response_real_mode(model):
    image = "dummy_base64_image"
    prompt = "Test prompt for LLM"
    mock_response = "{}"  # Should be ignored

    # Force real LLM mode
    model.should_use_mock_response = lambda: False

    # Mock model_controller.get_model_instance
    model.model_controller.get_model_instance = lambda img, prmpt: {
        "result": {"answer": "Generated by real model"},
        "llm_usage": {"tokens_used": 20}
    }

    # Run the function
    result = model.process_llm_response(image, prompt, mock_response)

    # Assertions
    assert isinstance(result, dict)
    assert "result" in result
    assert result["result"]["answer"] == "Generated by real model"
    assert "llm_usage" in result
    print("Real Mode LLM Response:", result)

def test_get_validation_details_exception_handled():
    instance = ImageQualityAnalysisModel()

    # Create a fake object that passes isinstance(photo_attributes, dict) check
    class FakeDict(dict):
        def items(self):
            raise ValueError("Boom!")

    photo_attributes = FakeDict()  # Looks like a dict, but items() will explode
    processed_params = {}

    result = instance.get_validation_details(photo_attributes, processed_params)

    assert result == ([], [])  # Should fall back to empty lists


def test_gray_image_brightness_outer_exception():
    instance = ImageQualityAnalysisModel()

    # Create a fake file-like object with valid `.content`
    class FakeFile:
        @property
        def content(self):
            return b"valid-but-we'll-break-numpy-later"

    fake_file = FakeFile()

    # Patch np.array to raise an exception after image is opened
    with patch("app.img_process.quality.process.np.array", side_effect=RuntimeError("Numpy boom")):
        result = instance.gray_image_brightness(fake_file)

    assert result is None  # Because we hit the outer exception


def test_calculate_sharpness_outer_exception():
    instance = ImageQualityAnalysisModel()

    # Create a dummy image and wrap it in a fake response with `.content`
    dummy_image = Image.new("RGB", (10, 10), color="white")
    buffer = BytesIO()
    dummy_image.save(buffer, format="JPEG")
    buffer.seek(0)

    class FakeResponse:
        content = buffer.getvalue()

    # Patch cv2.Laplacian to simulate crash AFTER image loads fine
    with patch("app.img_process.quality.process.cv2.Laplacian", side_effect=RuntimeError("cv2 crash")):
        result = instance.calculate_sharpness(FakeResponse())

    assert result is None  # Because it should hit the outer exception


# 1️⃣ Prompt missing
def test_get_watermark_validate_prompt_missing(model):
    with patch("app.img_process.quality.process.PromptLoader.get_prompt", return_value=None):
        result = model.get_watermark_validate("test watermark", "base64string")
    assert result["status"] == ResponseStatus.FAIL.value
    assert "Missing watermark validation prompt" in result["message"]


#  Prompt formatting error
def test_get_watermark_validate_formatting_error(model):
    mock_prompt = MagicMock()
    mock_prompt.format.side_effect = ValueError("format error")

    with patch("app.img_process.quality.process.PromptLoader.get_prompt", return_value=mock_prompt):
        result = model.get_watermark_validate("test watermark", "base64string")

    assert result["status"] == ResponseStatus.FAIL.value
    assert "Prompt formatting error" in result["message"]


#  Model invocation error (inside try-except around model call)
def test_get_watermark_validate_model_exception(model):
    with patch("app.img_process.quality.process.PromptLoader.get_prompt", return_value="{watermark_text}"), \
         patch.object(model, "should_use_mock_response", return_value=False), \
         patch.object(model.model_controller, "get_model_instance", side_effect=RuntimeError("model crashed")):
        result = model.get_watermark_validate("test watermark", "base64string")

    assert result["status"] == ResponseStatus.FAIL.value
    assert "ERROR:" in result["message"]


# Outer exception (simulate crash outside everything)
def test_get_watermark_validate_outer_exception(model):
    with patch("app.img_process.quality.process.PromptLoader.get_prompt", side_effect=Exception("catastrophic failure")):
        result = model.get_watermark_validate("test watermark", "base64string")

    assert result["status"] == ResponseStatus.FAIL.value
    assert "Unexpected error" in result["message"]

def test_get_fuzzy_ratio_raises_exception(model):
    # Patch `pre_configs.get` to raise an exception inside try block
    with patch("app.img_process.quality.process.pre_configs", side_effect=Exception("Simulated failure")):
        result = model.get_fuzzy_ratio("some watermark")

    assert result == (False, "")

def test_process_images_exception_handling(model):
    # Patch download_and_encode_image to raise an exception
    with patch.object(model, 'download_and_encode_image', side_effect=Exception("Simulated download error")):
        result = model.process_images("some_image_url", {}, {}, {})

    # Check that the error handling in process_images is triggered
    assert result["status"] == "FAILED"
    assert "Unexpected error occurred" in result["message"]


def test_process_images_success(model):
    image_url = "https://example.com/dummy.jpg"
    attributes = {"attr": "value"}
    processed_params = {
        "distortion": "",
        "rotation": "",
        "people_on_photo": "",
        "watermark": "",
        "room_under_construction": "",
        "is_it_a_render": "",
        "is_it_boxed": "",
        "furnished": "",
        "photo_classification": "",
        "room_size": "",
        "room_category": "",
        "brightness": "",
        "quality_of_picture": ""
    }
    other_vals = {
        "photo_classification": ["dining room", "living room"]
    }

    # Fake image content
    dummy_image_bytes = BytesIO()
    image = Image.new("RGB", (100, 100), color="white")
    image.save(dummy_image_bytes, format="JPEG")
    dummy_image_bytes.seek(0)
    dummy_image_content = dummy_image_bytes.getvalue()

    with patch.object(model, "download_and_encode_image") as mock_download_image, \
         patch.object(model, "analyze_image_quality") as mock_analyze_quality, \
         patch.object(model, "get_new_prompt") as mock_get_prompt, \
         patch.object(model, "process_llm_response") as mock_process_llm, \
         patch.object(model, "process_watermark_detection") as mock_watermark_detection, \
         patch("app.img_process.quality.process.get_boxed") as mock_get_boxed:

        # Setup return values
        mock_response = MagicMock()
        mock_response.content = dummy_image_content
        mock_download_image.return_value = (mock_response, "base64image==")

        mock_analyze_quality.return_value = (None, {"total_quality": 7}, 5)

        mock_get_prompt.return_value = (
            "Initial prompt here", "Watermark prompt here"
        )

        mock_process_llm.return_value = {
            "result": {
                "rotation": False,
                "quality_of_picture": 8,
                "distortion": 2,
                "is_it_boxed": False,
                "people_on_photo": False,
                "photo_classification": "dining room",
                "furnished": True,
                "room_size": "medium",
                "is_it_a_render": False,
                "room_under_construction": False,
                "room_category": "Modern"
            },
            "llm_usage": {"tokens_used": 50}
        }

        mock_watermark_detection.return_value = {
            "result": {
                "watermark": True,
                "watermark_text": "Some watermark text"
            },
            "llm_usage": {"tokens_used": 30}
        }

        mock_get_boxed.return_value = True

        result = model.process_images(image_url, attributes, processed_params, other_vals)

        # Assert status and keys
        assert result["status"] == "SUCCESS"
        assert result["message"] == "Processing completed successfully"
        assert isinstance(result["data"], dict)
        assert "watermark" in result["data"]
        assert "llm_usage" in result

        print("Process Images Output:", result)