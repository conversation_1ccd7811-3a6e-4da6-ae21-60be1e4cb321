[{"id": 1, "param_name": "distortion", "param_type": "int", "param_max_value": 0, "param_min_value": 0, "param_prompt": "distortion: Assess the provided image for geometric distortion. Evaluate aspects like warping, stretching, or perspective errors. Provide a detailed assessment with a description or score of the geometric distortion level on a scale from 1 (minimal) to 10 (severe). Output - Rate from 1 to 10", "created_at": "2024-07-30 10:58:21.730", "updated_at": "2024-07-30 10:58:21.730"}, {"id": 2, "param_name": "brightness", "param_type": "int", "param_max_value": 0, "param_min_value": 0, "param_prompt": "brightness: Rate the brightness of the picture from 1 - 10. Output - integer.", "created_at": "2024-08-09 10:41:49.618", "updated_at": "2024-08-09 10:43:01.675"}, {"id": 3, "param_name": "quality_of_picture", "param_type": "int", "param_max_value": 0, "param_min_value": 0, "param_prompt": "quality_of_picture: Rate the quality of picture based on clarity as seen on 3840x2160 screen from 1 - 10. Output - integer.", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:21:15.141"}, {"id": 4, "param_name": "rotation", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "rotation: Is the image rotated? Output - boolean", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:22:25.096"}, {"id": 5, "param_name": "people_on_photo", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "people_on_photo: Are there any people in the photo? Output - boolean", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:22:59.040"}, {"id": 6, "param_name": "watermark", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "watermark: Is there any watermark on the photo? Output - boolean", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:23:37.514"}, {"id": 7, "param_name": "watermark_text", "param_type": "str", "param_max_value": 0, "param_min_value": 0, "param_prompt": "watermark_text: If there is a watermark detected on the image, extract all the text associated with it. Output - String", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:24:18.302"}, {"id": 8, "param_name": "room_under_construction", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "room_under_construction: Is this a photo of room or a property under construction? Output - boolean", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:26:05.852"}, {"id": 9, "param_name": "furnished", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "furnished: Is the property in the photo furnished? Output - boolean", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:28:10.221"}, {"id": 10, "param_name": "room_category", "param_type": "str", "param_max_value": 0, "param_min_value": 0, "param_prompt": "room_category: What is the room category based on the standard of living. Pick only one from the list - ", "created_at": "2024-08-09 11:20:18.722", "updated_at": "2024-08-09 11:29:25.682"}, {"id": 11, "param_name": "is_it_a_render", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "is_it_a_render: Determine if the image is rendered or not rendered. Output - boolean. \r\n - Definition of a rendered image is a digital image that has been generated or produced using computer software \r\n - Examine the shadows of elements like (humans, buildings, or any other objects in the image), reflection, unusually clean edges, unnatural or artificial details in humans, buildings, or objects (such as trees, tables, chairs, pools, beds, cars, gardens, parks), or any artificially created or unrealistic elements like buildings, pools, environments, surroundings, or any unnatural or artificial scenarios.", "created_at": "2024-08-09 11:36:02.811", "updated_at": "2024-08-09 11:37:04.342"}, {"id": 12, "param_name": "is_it_boxed", "param_type": "Bool", "param_max_value": 0, "param_min_value": 0, "param_prompt": "is_it_boxed: Analyse and determine if the image of the property is altered into a collage of single image or not, by following below instructions. Output - boolean (True or False). \r\n - Clearly distinguish between a big watermark and a boxed image.", "created_at": "2024-08-09 11:36:02.811", "updated_at": "2024-08-09 11:37:48.035"}, {"id": 13, "param_name": "room_size", "param_type": "str", "param_max_value": 0, "param_min_value": 0, "param_prompt": "room_size: What is the size of the room? Pick one value from the list - [small, medium, large]. \r\n - Return 'unidentified room size', when no room is spotted or only a portion of the room is spotted in the image. \r\n - Pick one from the list, only when the whole room is spotted in the image: [small, medium, large].", "created_at": "2024-08-09 11:36:02.811", "updated_at": "2024-08-09 11:38:50.318"}, {"id": 14, "param_name": "photo_classification", "param_type": "str", "param_max_value": 0, "param_min_value": 0, "param_prompt": "photo_classification: Pick and classify the image into only one value if it is relevant to the image, from the given list - ", "created_at": "2024-08-12 12:19:32.051", "updated_at": "2024-08-12 12:20:45.936"}]