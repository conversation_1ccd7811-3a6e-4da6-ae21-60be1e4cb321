import time
import json
import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, mock_open
from decimal import Decimal

from core.utils.utils import (
    remove_duplicate_images,
    attributes_validations,
    get_current_timestamp,
    get_images_to_process,
    get_json_value,
    load_parameters,
    get_ttl_timestamp,
    filter_already_processed_images,
    convert_floats_to_decimals,
    calculate_llm_usage_metrics,
    calculate_llm_cost,
    update_llm_cost,
    put_message_in_notification_success_queue,
    delete_message_from_request_processing_queue,
)
from app.img_process.quality.schema import ImageDetails, PhotoAttributes
from core.utils.utils import (
    get_listing_record_from_dynamodb,
    add_listing_record_to_dynamodb,
)
from core.enums.status import ResponseStatus


@pytest.fixture(autouse=True)
def patch_token_costs(monkeypatch):
    monkeypatch.setattr("core.utils.utils.input_token_cost_per_1k", 0.002)
    monkeypatch.setattr("core.utils.utils.output_token_cost_per_1k", 0.004)


def test_add_listing_record_to_dynamodb(dynamodb_mock):
    submission_id = "test-123"
    data = {
        "api_type": "IMAGE_VALIDATION",
        "output": json.dumps({"response": "example"}),
        "payload": json.dumps({"query": "example"}),
        "status": ResponseStatus.SUCCESS.value,
        "timestamp": int(time.time()),
        "ttl": int(time.time()) + 3600,
    }

    response = add_listing_record_to_dynamodb(submission_id, data)

    assert response["ResponseMetadata"]["HTTPStatusCode"] == 200

    stored_item = dynamodb_mock.get_item(Key={"RequestID": submission_id}).get("Item")
    assert stored_item is not None
    for key in data:
        assert stored_item[key] == data[key]


def test_get_listing_record_from_dynamodb(dynamodb_mock):
    submission_id = "test-123"
    data = {
        "RequestID": submission_id,
        "api_type": "IMAGE_VALIDATION",
        "output": json.dumps({"response": "example"}),
        "payload": json.dumps({"query": "example"}),
        "status": ResponseStatus.SUCCESS.value,
        "timestamp": int(time.time()),
        "ttl": int(time.time()) + 1800,
    }

    dynamodb_mock.put_item(Item=data)

    item = get_listing_record_from_dynamodb(submission_id)

    assert item is not None
    for key in data:
        assert item[key] == data[key]


def test_remove_duplicate_images():
    image1 = ImageDetails(id=1, src="https://image.com/1.jpg")
    image2 = ImageDetails(id=2, src="https://image.com/2.jpg")
    image3 = ImageDetails(id=3, src="https://image.com/1.jpg")  # Duplicate of image1
    image4 = ImageDetails(id=4, src="https://image.com/3.jpg")
    image5 = ImageDetails(id=5, src="https://image.com/2.jpg")  # Duplicate of image2

    input_images = [image1, image2, image3, image4, image5]
    unique_images, duplicates = remove_duplicate_images(input_images)

    assert len(unique_images) == 3
    assert len(duplicates) == 2

    # Ensure the correct unique image IDs
    unique_ids = {img.id for img in unique_images}
    assert unique_ids == {1, 2, 4}

    # Ensure the duplicate messages reference the correct IDs
    duplicate_ids = {dup["id"] for dup in duplicates}
    assert duplicate_ids == {3, 5}


@pytest.mark.parametrize(
    "attributes, expected",
    [
        # All valid
        (
            PhotoAttributes(
                listing_id=123,
                country_code="AE",
                validate_params=[1, 2],
                images=[ImageDetails(id=1, src="https://img.com/1.jpg")],
            ),
            None,
        ),
        # Not a PhotoAttributes object
        (
            "not-a-photo-attributes-object",
            "Invalid attributes object.",
        ),
        # Invalid listing_id
        (
            PhotoAttributes(
                listing_id=-1,
                country_code="AE",
                validate_params=[1],
                images=[ImageDetails(id=1, src="https://img.com/1.jpg")],
            ),
            "Listing Id is not valid",
        ),
        # Invalid country_code
        (
            PhotoAttributes(
                listing_id=123,
                country_code="   ",
                validate_params=[1],
                images=[ImageDetails(id=1, src="https://img.com/1.jpg")],
            ),
            "Country Code is not valid",
        ),
        # Empty images list
        (
            PhotoAttributes(
                listing_id=123,
                country_code="AE",
                validate_params=[1],
                images=[],
            ),
            "Images list is not valid",
        ),
    ],
)
def test_attributes_validations_cases(attributes, expected):
    assert attributes_validations(attributes) == expected


def test_get_current_timestamp_format():
    timestamp = get_current_timestamp()

    # Check if it returns a valid ISO format timestamp
    parsed = datetime.fromisoformat(timestamp)

    # Check if it's timezone-aware and in UTC
    assert parsed.tzinfo is not None
    assert parsed.tzinfo == timezone.utc


def test_get_images_to_process_with_valid_config(sample_images):
    with patch.dict("core.utils.utils.config", {"num_images_to_process": "3"}):
        processed = get_images_to_process(sample_images)
        assert len(processed) == 3
        assert all(isinstance(img, ImageDetails) for img in processed)


def test_get_images_to_process_with_no_config(sample_images):
    with patch.dict("core.utils.utils.config", {}, clear=True):  # Simulate missing key
        processed = get_images_to_process(sample_images)
        assert processed == sample_images


def test_get_images_to_process_with_invalid_config(sample_images, caplog):
    with patch.dict("core.utils.utils.config", {"num_images_to_process": "invalid"}):
        processed = get_images_to_process(sample_images)
        assert processed == sample_images
        assert any(
            "Invalid configuration value for 'images_batch'" in msg
            for msg in caplog.text.splitlines()
        )


def test_get_images_to_process_with_config_greater_than_list(sample_images):
    with patch.dict("core.utils.utils.config", {"num_images_to_process": "10"}):
        processed = get_images_to_process(sample_images)
        assert processed == sample_images


def test_get_json_value_from_config_json():
    json_string = """
    {
        "model_version": "bedrock-2023-05-31",
        "bedrock_max_tokens": "1024",
        "bedrock_temperature": "0.7",
        "null_param": null
    }
    """

    assert get_json_value(json_string, "model_version") == "bedrock-2023-05-31"
    assert get_json_value(json_string, "bedrock_max_tokens") == "1024"
    assert get_json_value(json_string, "bedrock_temperature") == "0.7"
    assert get_json_value(json_string, "null_param") is None
    assert get_json_value(json_string, "non_existent_key") is None


def test_load_parameters_reads_parameter_list():
    mock_config_data = [
        {
            "id": 1,
            "param_name": "distortion",
            "param_type": "int",
            "param_max_value": 0,
            "param_min_value": 0,
            "param_prompt": "distortion: Assess the provided image for geometric distortion. Evaluate aspects like warping, stretching, or perspective errors. Provide a detailed assessment with a description or score of the geometric distortion level on a scale from 1 (minimal) to 10 (severe). Output - Rate from 1 to 10",
            "created_at": "2024-07-30 10:58:21.730",
            "updated_at": "2024-07-30 10:58:21.730",
        },
        {
            "id": 2,
            "param_name": "brightness",
            "param_type": "int",
            "param_max_value": 0,
            "param_min_value": 0,
            "param_prompt": "brightness: Rate the brightness of the picture from 1 - 10. Output - integer.",
            "created_at": "2024-08-09 10:41:49.618",
            "updated_at": "2024-08-09 10:43:01.675",
        },
    ]

    mock_file_content = json.dumps(mock_config_data)

    with patch("builtins.open", mock_open(read_data=mock_file_content)):
        with patch("core.utils.utils.CONFIG_FILE", "mocked_config.json"):
            result = load_parameters()
            assert isinstance(result, list)
            assert len(result) == 2
            assert result[0]["param_name"] == "distortion"
            assert result[1]["param_prompt"].startswith("brightness:")


def test_get_ttl_timestamp(monkeypatch):
    monkeypatch.setattr("core.utils.utils.dynamodb_record_ttl", "2")

    now = datetime.now(timezone.utc)
    expected = now + timedelta(hours=2)

    ttl_timestamp = get_ttl_timestamp()

    # Convert the integer Unix timestamp back to datetime
    actual = datetime.fromtimestamp(ttl_timestamp, tz=timezone.utc)

    # Allow slight delay tolerance
    delta = abs((expected - actual).total_seconds())
    assert delta < 5  # within 5 seconds tolerance


class MockImage:
    def __init__(self, id):
        self.id = id


def test_filter_already_processed_images():
    # Sample DynamoDB record simulating prior output
    dynamodb_record = {
        "output": json.dumps(
            {
                "images": ["image1.jpg", "image2.jpg"],
                "throttled_records": [{"id": "img_001"}, {"id": "img_003"}],
            }
        )
    }

    # Images to process (some match throttled IDs)
    images = [MockImage("img_001"), MockImage("img_002"), MockImage("img_003")]

    processed_images, images_to_reprocess = filter_already_processed_images(
        dynamodb_record, images
    )

    # Assertions
    assert processed_images == ["image1.jpg", "image2.jpg"]
    assert len(images_to_reprocess) == 2
    assert {img.id for img in images_to_reprocess} == {"img_001", "img_003"}


def test_convert_float():
    assert convert_floats_to_decimals(1.23) == Decimal("1.23")


def test_convert_nested_dict():
    data = {"a": 1.23, "b": {"c": 4.56, "d": [7.89, {"e": 0.12}]}}
    result = convert_floats_to_decimals(data)
    assert result["a"] == Decimal("1.23")
    assert result["b"]["c"] == Decimal("4.56")
    assert result["b"]["d"][0] == Decimal("7.89")
    assert result["b"]["d"][1]["e"] == Decimal("0.12")


def test_convert_list_with_mixed_types():
    data = [1.0, "test", 2.5, {"x": 3.3}]
    result = convert_floats_to_decimals(data)
    assert result[0] == Decimal("1.0")
    assert result[1] == "test"
    assert result[2] == Decimal("2.5")
    assert result[3]["x"] == Decimal("3.3")


def test_convert_tuple():
    data = (4.4, 5.5)
    result = convert_floats_to_decimals(data)
    assert result == [Decimal("4.4"), Decimal("5.5")]


def test_convert_non_float_values():
    assert convert_floats_to_decimals("string") == "string"
    assert convert_floats_to_decimals(42) == 42
    assert convert_floats_to_decimals(None) is None


def test_calculate_llm_usage_metrics_basic():
    images = [
        {
            "llm_usage": {
                "model1": {"inputTokens": 1000, "outputTokens": 500, "latencyMs": 120}
            }
        },
        {
            "llm_usage": {
                "model1": {"inputTokens": 2000, "outputTokens": 1500, "latencyMs": 250}
            }
        },
    ]

    result = calculate_llm_usage_metrics(images)

    assert result["inputTokens"] == 3000
    assert result["outputTokens"] == 2000
    assert result["latencyMs"] == 370
    assert result["inputTokenCost"] == round((3000 / 1000) * 0.002, 4)
    assert result["outputTokenCost"] == round((2000 / 1000) * 0.004, 4)
    assert result["totalCost"] == round(
        result["inputTokenCost"] + result["outputTokenCost"], 4
    )


def test_calculate_llm_usage_metrics_with_empty_and_missing_data():
    images = [
        {"llm_usage": {}},
        {"llm_usage": {"model2": None}},
        {"llm_usage": {"model3": {"inputTokens": 100, "outputTokens": 200}}},
    ]

    result = calculate_llm_usage_metrics(images)

    assert result["inputTokens"] == 100
    assert result["outputTokens"] == 200
    assert result["latencyMs"] == 0
    assert result["inputTokenCost"] == round((100 / 1000) * 0.002, 4)
    assert result["outputTokenCost"] == round((200 / 1000) * 0.004, 4)
    assert result["totalCost"] == round(
        result["inputTokenCost"] + result["outputTokenCost"], 4
    )


def test_calculate_llm_usage_metrics_no_costs(monkeypatch):
    # Patch cost constants to None
    monkeypatch.setattr("core.utils.utils.input_token_cost_per_1k", None)
    monkeypatch.setattr("core.utils.utils.output_token_cost_per_1k", None)

    images = [
        {
            "llm_usage": {
                "model": {"inputTokens": 1000, "outputTokens": 1000, "latencyMs": 100}
            }
        }
    ]

    result = calculate_llm_usage_metrics(images)

    assert result["inputTokens"] == 1000
    assert result["outputTokens"] == 1000
    assert result["latencyMs"] == 100
    assert result["inputTokenCost"] == 0.0
    assert result["outputTokenCost"] == 0.0
    assert result["totalCost"] == 0.0


def test_calculate_llm_cost():
    llm_usage_dict = {"inputTokens": 1000, "outputTokens": 500}
    result = calculate_llm_cost(llm_usage_dict)

    assert result == {"inputCost": 0.002, "outputCost": 0.002, "totalCost": 0.004}


def test_calculate_llm_cost_zero_tokens():
    result = calculate_llm_cost({"inputTokens": 0, "outputTokens": 0})

    assert result == {
        "inputCost": 0.0,
        "outputCost": 0.0,
        "totalCost": 0.0,
    }


def test_calculate_llm_cost_missing_keys():
    result = calculate_llm_cost({})  # No keys present

    assert result == {
        "inputCost": 0.0,
        "outputCost": 0.0,
        "totalCost": 0.0,
    }


def test_update_llm_cost():
    images = [
        {
            "llm_usage": {
                "model_1": {
                    "inputTokens": 1000,
                    "outputTokens": 500,
                }
            }
        },
        {
            "llm_usage": {
                "model_2": {
                    "inputTokens": 2000,
                    "outputTokens": 1000,
                }
            }
        },
    ]

    update_llm_cost(images)

    assert images[0]["llm_usage"]["model_1"]["cost"] == {
        "inputCost": 0.002,
        "outputCost": 0.002,
        "totalCost": 0.004,
    }
    assert images[1]["llm_usage"]["model_2"]["cost"] == {
        "inputCost": 0.004,
        "outputCost": 0.004,
        "totalCost": 0.008,
    }


@patch("core.utils.utils.sqs_client")
@patch("core.utils.utils.NOTIFICATION_SUCESS_QUEUE_URL", "https://dummy-queue-url")
@patch("core.utils.utils.DecimalEncoder")
def test_put_message_in_notification_success_queue_success(
    mock_encoder, mock_sqs_client
):
    submission_id = "test123"
    response = {"message": "Success"}

    with patch(
        "core.utils.utils.json.dumps", return_value='{"message": "Success"}'
    ) as mock_json:
        put_message_in_notification_success_queue(submission_id, response)

        mock_sqs_client.send_message.assert_called_once_with(
            QueueUrl="https://dummy-queue-url", MessageBody='{"message": "Success"}'
        )
        mock_json.assert_called_once_with(
            response, cls=mock_encoder, ensure_ascii=False
        )


@patch("core.utils.utils.sqs_client")
@patch("core.utils.utils.NOTIFICATION_SUCESS_QUEUE_URL", "https://dummy-queue-url")
@patch("core.utils.utils.DecimalEncoder")
def test_put_message_in_notification_success_queue_exception(
    mock_encoder, mock_sqs_client
):
    submission_id = "fail456"
    response = {"message": "Fail"}

    mock_sqs_client.send_message.side_effect = Exception("SQS Send Failed")

    with patch("core.utils.utils.json.dumps", return_value='{"message": "Fail"}'):
        with pytest.raises(
            Exception,
            match="Failed to add message in notification queue fail456: SQS Send Failed",
        ):
            put_message_in_notification_success_queue(submission_id, response)

        mock_sqs_client.send_message.assert_called_once()


@patch("core.utils.utils.sqs_client")
@patch(
    "core.utils.utils.REQUEST_PROCESSING_QUEUE_URL",
    "https://dummy-processing-queue-url",
)
def test_delete_message_success(mock_sqs_client):
    submission_id = "abc123"
    receipt_handle = "dummy-receipt-handle"

    delete_message_from_request_processing_queue(submission_id, receipt_handle)

    mock_sqs_client.delete_message.assert_called_once_with(
        QueueUrl="https://dummy-processing-queue-url", ReceiptHandle=receipt_handle
    )


@patch("core.utils.utils.sqs_client")
def test_delete_message_skipped_when_no_receipt_handle(mock_sqs_client):
    submission_id = "nohandle"
    receipt_handle = None

    # Should skip deletion
    delete_message_from_request_processing_queue(submission_id, receipt_handle)

    mock_sqs_client.delete_message.assert_not_called()


@patch("core.utils.utils.sqs_client")
@patch(
    "core.utils.utils.REQUEST_PROCESSING_QUEUE_URL",
    "https://dummy-processing-queue-url",
)
def test_delete_message_raises_exception(mock_sqs_client):
    submission_id = "fail456"
    receipt_handle = "bad-handle"

    mock_sqs_client.delete_message.side_effect = Exception("Delete Failed")

    with pytest.raises(
        Exception,
        match=f"Failed to delete message from request processing queue {submission_id}: Delete Failed",
    ):
        delete_message_from_request_processing_queue(submission_id, receipt_handle)
