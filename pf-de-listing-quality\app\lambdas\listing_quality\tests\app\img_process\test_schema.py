import os
import sys

sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../"))
)
from app.img_process.quality.schema import (
    ImageQualityBase,
    ImageQualityCreate,
    ImageQuality,
    ImageProcessParametersBase,
    ImageProcessParametersCreate,
    ImageProcessParameters,
    ImageParametersJunctionBase,
    ImageParametersJunctionCreate,
    ImageParametersJunction,
    PhotoAttributes,
    ImageDetails,
)

import pytest
from datetime import datetime
from pydantic import ValidationError


def test_image_quality_base_fields():
    model = ImageQualityBase(
        img_id=1, listing_id=100, og_url="https://example.com/image.jpg"
    )
    assert model.img_id == 1
    assert isinstance(model.created_at, datetime)
    assert isinstance(model.updated_at, datetime)


def test_image_quality_create_inherits_base():
    model = ImageQualityCreate(
        img_id=2, listing_id=101, og_url="https://example.com/image2.jpg"
    )
    assert isinstance(model, ImageQualityBase)


def test_image_quality_with_id():
    model = ImageQuality(
        id=10, img_id=1, listing_id=99, og_url="https://example.com/image.jpg"
    )
    assert model.id == 10
    assert model.img_id == 1


def test_image_process_parameters_base_fields():
    model = ImageProcessParametersBase(
        param_name="brightness",
        param_type="int",
        param_max_value=100,
        param_min_value=0,
        param_prompt="Rate brightness from 0 to 100",
    )
    assert model.param_name == "brightness"
    assert model.param_prompt.startswith("Rate")


def test_image_process_parameters_inheritance():
    model = ImageProcessParametersCreate(param_name="contrast", param_type="int")
    assert isinstance(model, ImageProcessParametersBase)


def test_image_process_parameters_with_id():
    model = ImageProcessParameters(id=1, param_name="saturation", param_type="int")
    assert model.id == 1


def test_image_parameters_junction_models():
    base = ImageParametersJunctionBase(image_db_id=1, param_id=2)
    assert base.image_db_id == 1

    create = ImageParametersJunctionCreate(image_db_id=1, param_id=3)
    assert isinstance(create, ImageParametersJunctionBase)

    full = ImageParametersJunction(id=5, image_db_id=1, param_id=3)
    assert full.id == 5


def test_photo_attributes_valid():
    image = ImageDetails(id=1, src="https://example.com/img1.jpg")
    model = PhotoAttributes(
        customer_type="pro",
        type="residential",
        listing_id=123,
        country_code="AE",
        submission_id="abc-123",
        message_handle="msg-456",
        validate_params=[1, 2, 3],
        images=[image],
        custom_input_list={"some_param": "value"},
    )
    assert model.listing_id == 123
    assert len(model.validate_params) == 3
    assert isinstance(model.images[0], ImageDetails)


def test_photo_attributes_missing_required_fields():
    with pytest.raises(ValidationError) as exc:
        PhotoAttributes()
    assert "images" in str(exc.value)


def test_image_details_validation():
    details = ImageDetails(id=5, src="https://example.com/img.jpg")
    assert details.id == 5
    assert str(details.src) == "https://example.com/img.jpg"


def test_invalid_image_url_raises_error():
    with pytest.raises(ValidationError):
        ImageDetails(id=6, src="not-a-url")
