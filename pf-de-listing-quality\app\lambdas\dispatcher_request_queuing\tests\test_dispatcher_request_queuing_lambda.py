import os
import json
import boto3
import pytest
from moto import mock_aws
from unittest.mock import patch, MagicMock
import logging

os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
os.environ["DIAMOND_QUEUE_URL"] = "https://sqs.region.amazonaws.com/account/diamond"
os.environ["RUBY_QUEUE_URL"] = "https://sqs.region.amazonaws.com/account/ruby"
os.environ["SAPPHIRE_QUEUE_URL"] = "https://sqs.region.amazonaws.com/account/sapphire"
os.environ["EMERALD_QUEUE_URL"] = "https://sqs.region.amazonaws.com/account/emerald"
os.environ["REQUEST_PROCESSING_QUEUE_URL"] = "https://sqs.region.amazonaws.com/account/processing"
os.environ["TOTAL_PROCESSING_SIZE"] = "100"
os.environ["DIAMOND_RATIO"] = "0.4"
os.environ["RUBY_RATIO"] = "0.3"
os.environ["SAPPHIRE_RATIO"] = "0.2"
os.environ["EMERALD_RATIO"] = "0.1"
os.environ["LISTING_QUALITY_INVOCATION_LAMBDA"] = "listing-quality-dispatcher-lambda-staging"

# Import the lambda function
import sys
sys.path.append('.')  # Adjust if needed to find your lambda code

mock_ssm_client = MagicMock()
mock_sqs_client = MagicMock()

# Patch boto3.client before importing the module under test
with patch('boto3.client') as mock_boto_client:
    mock_boto_client.side_effect = lambda service, **kwargs: (
        mock_ssm_client if service == 'ssm' 
        else mock_sqs_client if service == 'sqs' 
        else MagicMock()
    )
    import dispatcher_request_queuing
    from dispatcher_request_queuing import lambda_handler, allocate_messages, move_messages

@pytest.fixture
def mock_ssm():
    """Create a mock SSM client."""
    # Reset the mock before each test
    mock_ssm_client.reset_mock()
    return mock_ssm_client

@pytest.fixture
def mock_sqs():
    """Create a mock SQS client."""
    # Reset the mock before each test
    mock_sqs_client.reset_mock()
    return mock_sqs_client

# Set up test environment variables
@pytest.fixture(autouse=True)
def mock_env_variables():
    with patch.dict(os.environ, {
        "DIAMOND_QUEUE_URL": "https://sqs.region.amazonaws.com/account/diamond",
        "RUBY_QUEUE_URL": "https://sqs.region.amazonaws.com/account/ruby",
        "SAPPHIRE_QUEUE_URL": "https://sqs.region.amazonaws.com/account/sapphire",
        "EMERALD_QUEUE_URL": "https://sqs.region.amazonaws.com/account/emerald",
        "REQUEST_PROCESSING_QUEUE_URL": "https://sqs.region.amazonaws.com/account/processing",
        "TOTAL_PROCESSING_SIZE": "100",
        "DIAMOND_RATIO": "0.4",
        "RUBY_RATIO": "0.3",
        "SAPPHIRE_RATIO": "0.2",
        "EMERALD_RATIO": "0.1",
        "LISTING_QUALITY_INVOCATION_LAMBDA": "listing-quality-dispatcher-lambda-staging"
    }):
        yield

# SQS fixture for mocking AWS SQS
@pytest.fixture
def sqs():
    with mock_aws():
        sqs_client = boto3.client("sqs", region_name="us-east-1")
        yield sqs_client

# Create test queues
@pytest.fixture
def setup_queues(sqs):
    queues = {}
    # Create priority queues
    for queue_name in ['diamond', 'ruby', 'sapphire', 'emerald']:
        response = sqs.create_queue(QueueName=queue_name)
        queues[queue_name] = response['QueueUrl']
    
    # Create processing queue
    response = sqs.create_queue(QueueName='processing')
    queues['processing'] = response['QueueUrl']
    
    return queues

# Test allocate_messages function
def test_allocate_messages():
    priority_counts = {
        "diamond": 50,
        "ruby": 40,
        "sapphire": 30,
        "emerald": 20
    }
    empty_space = 100
    
    allocated = allocate_messages(priority_counts, empty_space)
    
    # Check allocation based on ratios
    assert allocated["diamond"] == 40  # 40% of 100
    assert allocated["ruby"] == 30     # 30% of 100
    assert allocated["sapphire"] == 20 # 20% of 100
    assert allocated["emerald"] == 10  # 10% of 100
    assert sum(allocated.values()) == 100  # All space allocated

def test_allocate_messages_with_limited_messages():
    priority_counts = {
        "diamond": 20,  # Less than allocation would suggest
        "ruby": 40,
        "sapphire": 30,
        "emerald": 20
    }
    empty_space = 100
    
    allocated = allocate_messages(priority_counts, empty_space)
    
    # Check allocation based on messages available
    assert allocated["diamond"] == 20  # Limited by available messages
    assert allocated["ruby"] == 40     # Normal allocation
    assert allocated["sapphire"] == 30 # Normal allocation
    assert allocated["emerald"] == 10  # Normal allocation
    assert sum(allocated.values()) == 100

def test_allocate_messages_with_limited_space():
    priority_counts = {
        "diamond": 50,
        "ruby": 40,
        "sapphire": 30,
        "emerald": 20
    }
    empty_space = 50  # Less than total allocation would suggest
    
    allocated = allocate_messages(priority_counts, empty_space)
    
    # Check allocation based on ratios applied to limited space
    assert allocated["diamond"] == 20  # 40% of 50
    assert allocated["ruby"] == 15     # 30% of 50
    assert allocated["sapphire"] == 10 # 20% of 50
    assert allocated["emerald"] == 5   # 10% of 50
    assert sum(allocated.values()) == 50  # All limited space allocated

# Test the lambda_handler function when queues have messages
@patch('dispatcher_request_queuing.get_message_count')
@patch('dispatcher_request_queuing.fetch_messages')
@patch('dispatcher_request_queuing.send_messages')
@patch('dispatcher_request_queuing.delete_messages')
def test_lambda_handler_with_messages(mock_delete, mock_send, mock_fetch, mock_count):
    # Mock queue message counts
    def mock_get_count(queue_url):
        if queue_url == os.environ["REQUEST_PROCESSING_QUEUE_URL"]:
            return 20  # Processing queue has 20 messages
        elif queue_url == os.environ["DIAMOND_QUEUE_URL"]:
            return 50
        elif queue_url == os.environ["RUBY_QUEUE_URL"]:
            return 40
        elif queue_url == os.environ["SAPPHIRE_QUEUE_URL"]:
            return 30
        elif queue_url == os.environ["EMERALD_QUEUE_URL"]:
            return 20
        return 0
    
    mock_count.side_effect = mock_get_count
    
    # Mock fetching messages
    def mock_fetch_msgs(queue_url, count):
        return [{"Body": f"Message from {queue_url}", "ReceiptHandle": f"receipt-{i}"} for i in range(count)]
    
    mock_fetch.side_effect = mock_fetch_msgs
    
    # Execute lambda
    result = lambda_handler({}, {})
    
    # Verify message allocation and movement
    assert result == "Messages moved successfully."
    
    # Check if messages were fetched according to allocation
    expected_fetch_calls = [
        ((os.environ["DIAMOND_QUEUE_URL"], 32),),  # 40% of 80 empty space
        ((os.environ["RUBY_QUEUE_URL"], 24),),     # 30% of 80 empty space
        ((os.environ["SAPPHIRE_QUEUE_URL"], 16),), # 20% of 80 empty space
        ((os.environ["EMERALD_QUEUE_URL"], 8),),   # 10% of 80 empty space
    ]
    assert mock_fetch.call_count == 4
    
    # Check if messages were sent to processing queue
    assert mock_send.call_count == 4
    
    # Check if source messages were deleted
    assert mock_delete.call_count == 4

# Test when processing queue is full
@patch('dispatcher_request_queuing.get_message_count')
def test_lambda_handler_processing_queue_full(mock_count):
    # Mock processing queue as full
    def mock_get_count(queue_url):
        if queue_url == os.environ["REQUEST_PROCESSING_QUEUE_URL"]:
            return 100  # Processing queue is full
        else:
            return 10  # Other queues have messages
    
    mock_count.side_effect = mock_get_count
    
    # Execute lambda
    result = lambda_handler({}, {})
    
    # Verify no messages were moved
    assert result == "Processing queue is full."

# Test when no messages in priority queues
@patch('dispatcher_request_queuing.get_message_count')
def test_lambda_handler_no_messages_in_priority(mock_count):
    # Mock no messages in priority queues
    def mock_get_count(queue_url):
        if queue_url == os.environ["REQUEST_PROCESSING_QUEUE_URL"]:
            return 20
        else:
            return 0  # No messages in priority queues
    
    mock_count.side_effect = mock_get_count
    
    # Execute lambda
    result = lambda_handler({}, {})
    
    # Verify proper result
    assert result == "No messages to move."

# Integration test with actual mocked SQS queues
@patch.dict(os.environ, {
    "DIAMOND_QUEUE_URL": "diamond",
    "RUBY_QUEUE_URL": "ruby",
    "SAPPHIRE_QUEUE_URL": "sapphire",
    "EMERALD_QUEUE_URL": "emerald",
    "REQUEST_PROCESSING_QUEUE_URL": "processing",
    "TOTAL_PROCESSING_SIZE": "100",
    "DIAMOND_RATIO": "0.4",
    "RUBY_RATIO": "0.3",
    "SAPPHIRE_RATIO": "0.2",
    "EMERALD_RATIO": "0.1"
})
@patch('dispatcher_request_queuing.sqs_client')
def test_move_messages(mock_aws_client, setup_queues):
    allocated = {
        "diamond": 10,
        "ruby": 5,
        "sapphire": 3,
        "emerald": 2
    }
    
    # Mock fetch_messages to return a list of messages
    def mock_fetch(queue_url, max_messages):
        return [{"Body": f"Message {i} from {queue_url}", "ReceiptHandle": f"receipt-{queue_url}-{i}"} 
                for i in range(max_messages)]
    
    mock_aws_client.receive_message.side_effect = lambda **kwargs: {
        "Messages": [{"Body": f"Message from {kwargs['QueueUrl']}", "ReceiptHandle": f"receipt-{i}"} 
                    for i in range(min(kwargs['MaxNumberOfMessages'], 10))]
    }
    
    # Call the function
    move_messages(allocated)
    
    # Verify messages were fetched, sent, and deleted as expected
    assert mock_aws_client.receive_message.call_count == 4
    assert mock_aws_client.send_message.call_count == 20  # Total allocated messages
    assert mock_aws_client.delete_message.call_count == 20

# Test error handling in get_message_count
@patch('dispatcher_request_queuing.sqs_client')
def test_get_message_count_error_handling(mock_aws_client):
    from dispatcher_request_queuing import get_message_count
    
    # Simulate throttling error
    class ThrottlingException(Exception):
        pass
    
    mock_aws_client.exceptions.RequestThrottled = ThrottlingException
    mock_aws_client.get_queue_attributes.side_effect = ThrottlingException("Throttled")
    
    # Function should return 0 if throttled
    result = get_message_count("some-queue-url")
    assert result == 0

# Test error handling in lambda_handler
@patch('dispatcher_request_queuing.get_message_count')
def test_lambda_handler_error_handling(mock_count):
    # Simulate an exception
    mock_count.side_effect = Exception("Test exception")
    
    # Execute lambda
    result = lambda_handler({}, {})
    
    # Verify error result
    assert result == "Error in Lambda execution."

def get_mock_messages(diamond, ruby, sapphire, emerald):
    mock_messages = {
        os.environ["DIAMOND_QUEUE_URL"]: [{"MessageId": f"diamond_message_{i+1}", "Body": json.dumps({"category": "diamond", "body": f"diamond_body_{i+1}"}), "ReceiptHandle": f"diamond_receipt_{i+1}"} for i in range(diamond)],
        os.environ["RUBY_QUEUE_URL"]: [{"MessageId": f"ruby_message_{i+1}", "Body": json.dumps({"category": "ruby", "body": f"ruby_body_{i+1}"}), "ReceiptHandle": f"ruby_receipt_{i+1}"} for i in range(ruby)],
        os.environ["SAPPHIRE_QUEUE_URL"]: [{"MessageId": f"sapphire_message_{i+1}", "Body": json.dumps({"category": "sapphire", "body": f"sapphire_body_{i+1}"}), "ReceiptHandle": f"sapphire_receipt_{i+1}"} for i in range(sapphire)],
        os.environ["EMERALD_QUEUE_URL"]: [{"MessageId": f"emerald_message_{i+1}", "Body": json.dumps({"category": "emerald", "body": f"emerald_body_{i+1}"}), "ReceiptHandle": f"emerald_receipt_{i+1}"} for i in range(emerald)]
    }
    return mock_messages


def trigger_lambda_handler(diamond, ruby, sapphire, emerald, processing, mock_sqs):
    """Test the full flow of the lambda_handler function with mocked AWS services."""

    diamond_messages = diamond
    ruby_messages = ruby
    sapphire_messages = sapphire
    emerald_messages = emerald

    mock_messages = get_mock_messages(
        diamond=diamond_messages,
        ruby=ruby_messages,
        sapphire=sapphire_messages,
        emerald=emerald_messages
    )

    diamond_queue_messages_count = len(mock_messages[os.environ["DIAMOND_QUEUE_URL"]])
    ruby_queue_messages_count = len(mock_messages[os.environ["RUBY_QUEUE_URL"]])
    sapphire_queue_messages_count = len(mock_messages[os.environ["SAPPHIRE_QUEUE_URL"]])
    emerald_queue_messages_count = len(mock_messages[os.environ["EMERALD_QUEUE_URL"]])

    priority_counts = {
        "diamond": diamond_queue_messages_count,
        "ruby": ruby_queue_messages_count,
        "sapphire": sapphire_queue_messages_count,
        "emerald": emerald_queue_messages_count
    }

    processing_queue_messages_count = processing
    processing_queue_messages_in_flight = 0
    empty_space = int(os.environ["TOTAL_PROCESSING_SIZE"]) - (processing_queue_messages_count + processing_queue_messages_in_flight)
    
    # Mock queue attributes responses for priority queues
    queue_attributes = {
        os.environ["DIAMOND_QUEUE_URL"]: {"ApproximateNumberOfMessages": diamond_queue_messages_count},
        os.environ["RUBY_QUEUE_URL"]: {"ApproximateNumberOfMessages": ruby_queue_messages_count},
        os.environ["SAPPHIRE_QUEUE_URL"]: {"ApproximateNumberOfMessages": sapphire_queue_messages_count},
        os.environ["EMERALD_QUEUE_URL"]: {"ApproximateNumberOfMessages": emerald_queue_messages_count},
        os.environ["REQUEST_PROCESSING_QUEUE_URL"]: {
            "ApproximateNumberOfMessages": processing_queue_messages_count,
            "ApproximateNumberOfMessagesNotVisible": processing_queue_messages_in_flight
        }
    }
    
    def get_queue_attributes_side_effect(QueueUrl, AttributeNames):
        return {"Attributes": queue_attributes.get(QueueUrl, {})}
    
    def receive_message_side_effect(QueueUrl, MaxNumberOfMessages, WaitTimeSeconds):
        messages = mock_messages.get(QueueUrl, [])
        return {"Messages": messages[:MaxNumberOfMessages]} if messages else {}
    
    mock_sqs.get_queue_attributes.side_effect = get_queue_attributes_side_effect
    mock_sqs.receive_message.side_effect = receive_message_side_effect

    # Mock sent messages
    sent_messages = []

    def send_message_side_effect(**kwargs):
        sent_messages.append(json.loads(kwargs["MessageBody"]))
        return {"MessageId": "mocked-id"}

    mock_sqs.send_message.side_effect = send_message_side_effect

    # Call allocate_messages directly
    allocation = allocate_messages(priority_counts, empty_space)
    
    # Call the lambda handler
    result = lambda_handler({}, {})
    
    # Verify the function executed successfully
    assert result == "Messages moved successfully."

    # Verify actual number of messages sent
    sent_counts = {"diamond": 0, "ruby": 0, "sapphire": 0, "emerald": 0}

    for msg in sent_messages:
        category = msg.get("category")
        sent_counts[category] += 1

    total_sent = sum(sent_counts.values())
    assert total_sent <= empty_space

    assert sent_counts == allocation
    
    # Verify get_queue_attributes was called for all queues
    assert mock_sqs.get_queue_attributes.call_count == 5
    
    # Verify receive_message, send_message, and delete_message were called appropriately
    assert mock_sqs.receive_message.call_count > 0
    assert mock_sqs.send_message.call_count > 0
    assert mock_sqs.delete_message.call_count > 0

def test_lambda_handler_normal_flow(mock_sqs):
    diamond = 10
    ruby = 10
    sapphire = 10
    emerald = 10
    processing = 0
    trigger_lambda_handler(diamond, ruby, sapphire, emerald, processing, mock_sqs)

def test_lambda_handler_diamond_empty_flow(mock_sqs):
    diamond = 0
    ruby = 10
    sapphire = 10
    emerald = 10
    processing = 0
    trigger_lambda_handler(diamond, ruby, sapphire, emerald, processing, mock_sqs)

def test_lambda_handler_ruby_empty_flow(mock_sqs):
    diamond = 10
    ruby = 0
    sapphire = 10
    emerald = 10
    processing = 0
    trigger_lambda_handler(diamond, ruby, sapphire, emerald, processing, mock_sqs)

def test_lambda_handler_sapphire_empty_flow(mock_sqs):
    diamond = 10
    ruby = 10
    sapphire = 0
    emerald = 10
    processing = 0
    trigger_lambda_handler(diamond, ruby, sapphire, emerald, processing, mock_sqs)

def test_lambda_handler_emerald_empty_flow(mock_sqs):
    diamond = 10
    ruby = 10
    sapphire = 10
    emerald = 0
    processing = 0
    trigger_lambda_handler(diamond, ruby, sapphire, emerald, processing, mock_sqs)

def test_lambda_handler_no_messages(mock_sqs):
    """Test the lambda_handler when there are no messages in the priority queues."""

    diamond_messages = 0
    ruby_messages = 0
    sapphire_messages = 0
    emerald_messages = 0

    mock_messages = get_mock_messages(
        diamond=diamond_messages,
        ruby=ruby_messages,
        sapphire=sapphire_messages,
        emerald=emerald_messages
    )

    diamond_queue_messages_count = len(mock_messages[os.environ["DIAMOND_QUEUE_URL"]])
    ruby_queue_messages_count = len(mock_messages[os.environ["RUBY_QUEUE_URL"]])
    sapphire_queue_messages_count = len(mock_messages[os.environ["SAPPHIRE_QUEUE_URL"]])
    emerald_queue_messages_count = len(mock_messages[os.environ["EMERALD_QUEUE_URL"]])
    
    # Mock empty queue attributes responses
    queue_attributes = {
        os.environ["DIAMOND_QUEUE_URL"]: {"ApproximateNumberOfMessages": diamond_queue_messages_count},
        os.environ["RUBY_QUEUE_URL"]: {"ApproximateNumberOfMessages": ruby_queue_messages_count},
        os.environ["SAPPHIRE_QUEUE_URL"]: {"ApproximateNumberOfMessages": sapphire_queue_messages_count},
        os.environ["EMERALD_QUEUE_URL"]: {"ApproximateNumberOfMessages": emerald_queue_messages_count}
    }
    
    def get_queue_attributes_side_effect(QueueUrl, AttributeNames):
        return {"Attributes": queue_attributes.get(QueueUrl, {})}
    
    mock_sqs.get_queue_attributes.side_effect = get_queue_attributes_side_effect
    
    # Call the lambda handler
    result = lambda_handler({}, {})
    
    # Verify the function detected no messages
    assert result == "No messages to move."
    
    # Verify get_queue_attributes was called for all priority queues
    assert mock_sqs.get_queue_attributes.call_count == 4
    
    # Verify no other SQS operations were performed
    mock_sqs.receive_message.assert_not_called()
    mock_sqs.send_message.assert_not_called()
    mock_sqs.delete_message.assert_not_called()


def test_lambda_handler_full_processing_queue(mock_sqs):
    """Test the lambda_handler when the processing queue is full."""

    diamond_messages = 1
    ruby_messages = 1
    sapphire_messages = 1
    emerald_messages = 1

    mock_messages = get_mock_messages(
        diamond=diamond_messages,
        ruby=ruby_messages,
        sapphire=sapphire_messages,
        emerald=emerald_messages
    )

    diamond_queue_messages_count = len(mock_messages[os.environ["DIAMOND_QUEUE_URL"]])
    ruby_queue_messages_count = len(mock_messages[os.environ["RUBY_QUEUE_URL"]])
    sapphire_queue_messages_count = len(mock_messages[os.environ["SAPPHIRE_QUEUE_URL"]])
    emerald_queue_messages_count = len(mock_messages[os.environ["EMERALD_QUEUE_URL"]])

    processing_queue_messages_count = 80
    processing_queue_messages_in_flight = 20
    
    # Mock queue attributes responses with messages in priority queues but a full processing queue
    queue_attributes = {
        os.environ["DIAMOND_QUEUE_URL"]: {"ApproximateNumberOfMessages": diamond_queue_messages_count},
        os.environ["RUBY_QUEUE_URL"]: {"ApproximateNumberOfMessages": ruby_queue_messages_count},
        os.environ["SAPPHIRE_QUEUE_URL"]: {"ApproximateNumberOfMessages": sapphire_queue_messages_count},
        os.environ["EMERALD_QUEUE_URL"]: {"ApproximateNumberOfMessages": emerald_queue_messages_count},
        os.environ["REQUEST_PROCESSING_QUEUE_URL"]: {
            "ApproximateNumberOfMessages": processing_queue_messages_count,
            "ApproximateNumberOfMessagesNotVisible": processing_queue_messages_in_flight
        }
    }
    
    def get_queue_attributes_side_effect(QueueUrl, AttributeNames):
        return {"Attributes": queue_attributes.get(QueueUrl, {})}
    
    mock_sqs.get_queue_attributes.side_effect = get_queue_attributes_side_effect
    
    # Call the lambda handler
    result = lambda_handler({}, {})
    
    # Verify the function detected a full processing queue
    assert result == "Processing queue is full."
    
    # Verify get_queue_attributes was called for all priority queues and the processing queue
    assert mock_sqs.get_queue_attributes.call_count == 5
    
    # Verify no other SQS operations were performed
    mock_sqs.receive_message.assert_not_called()
    mock_sqs.send_message.assert_not_called()
    mock_sqs.delete_message.assert_not_called()
