import json
import os

import boto3
from dotenv import load_dotenv

from core.logger.logger import get_logger, log_frame_info, log_frame_error

# Load environment variables from .env file
load_dotenv()

# Get AWS region from environment variable, default to "us-east-1" if not set
AWS_REGION = os.getenv("AWS_DEFAULT_REGION")  # Default to "us-east-1" if not set
# os.environ["AWS_DEFAULT_REGION"] = AWS_REGION

# Initialize AWS SSM (Systems Manager) client
ssm_client = boto3.client("ssm")

# Set up logger for logging information and errors
logger = get_logger(__name__)


# Class to handle custom configuration loading and retrieval from SSM and local config file
class CustomConfig:
    def __init__(self):
        # Resolve the absolute path to the config.json file
        self.config_file = self.get_absolute_path("utils/config.json")
        log_frame_info(
            logger, message="Config file path resolved: ", config_file=self.config_file
        )

        # Load configuration data from the config file
        self.config = self.load_config()
        log_frame_info(logger, "Configuration successfully loaded.")

    # Resolve the absolute path for the given relative path
    def get_absolute_path(self, relative_path):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        absolute_path = f"{current_dir}/{relative_path}"
        log_frame_info(
            logger,
            message="Resolved absolute path:  for relative path: ",
            absolute_path=absolute_path,
            relative_path=relative_path,
        )

        return absolute_path

    # Load and return the configuration from the local JSON file
    def load_config(self):
        log_frame_info(
            logger, message="Loading configuration from ", config_file=self.config_file
        )

        # Open the config file and load its contents as JSON
        with open(self.config_file, "r") as file:
            config = json.load(file)

        log_frame_info(logger, "Configuration loaded successfully")
        return config

    # Fetch multiple SSM parameters in batches from AWS SSM Parameter Store
    def get_ssm_parameters(self, ssm_param_names):
        try:
            log_frame_info(logger, "Fetching SSM parameters...")

            parameters = {}
            batch_size = 10  # SSM API supports a max of 10 parameters per request

            # Split the list of parameter names into batches of 10 and fetch each batch
            for i in range(0, len(ssm_param_names), batch_size):
                batch_names = ssm_param_names[i : i + batch_size]
                log_frame_info(
                    logger, message="Fetching batch: ", batch_names=batch_names
                )

                # Fetch parameters with decryption enabled (for sensitive data)
                response = ssm_client.get_parameters(
                    Names=batch_names, WithDecryption=True
                )
                # Update parameters dictionary with the fetched values
                parameters.update(
                    {param["Name"]: param["Value"] for param in response["Parameters"]}
                )

            log_frame_info(logger, "Successfully fetched SSM parameters.")
            return parameters
        except Exception as e:
            log_frame_error(
                logger,
                message="An error occurred while accessing the SSM parameters: ",
                error=e,
                exc_info=True,
            )
            return {}

    # Fetch a single SSM parameter from AWS SSM Parameter Store
    def get_ssm_parameter(self, ssm_param):
        try:
            log_frame_info(
                logger, message="Fetching SSM parameter: ", ssm_param=ssm_param
            )

            # Fetch the parameter with decryption enabled
            response = ssm_client.get_parameter(Name=ssm_param, WithDecryption=True)
            parameter_value = response["Parameter"]["Value"]

            log_frame_info(
                logger,
                message="Successfully fetched SSM parameter",
                ssm_param=ssm_param,
            )
            return parameter_value
        except Exception as e:
            log_frame_error(
                logger,
                message="Error fetching SSM parameter : ",
                ssm_param=ssm_param,
                error=e,
                exc_info=True,
            )
            return {}  # Return an empty dictionary in case of an error

    # Fetch environment-specific configuration from SSM Parameter Store
    def get_ssm_config(self, env):
        log_frame_info(logger, message="Fetching SSM config for environment: ", env=env)

        # Determine the appropriate SSM parameter names based on the environment
        if env == "dev":
            param_names = self.config["DEV_SSM"]
        elif env == "qa":
            param_names = self.config["QA_SSM"]
        elif env == "prod":
            param_names = self.config["PROD_SSM"]
        elif env == "local":
            param_names = self.config["LOCAL_SSM"]
            log_frame_info(
                logger, message="Fetching SSM parameters for local environment."
            )
            param_values = self.get_ssm_parameters(list(param_names.values()))

            # Construct the config dictionary from the fetched parameter values
            config = {
                "bedrock_prompt": param_values[param_names["bedrock_prompt"]],
                "bedrock_llm_endpoint_name": param_values[
                    param_names["bedrock_llm_endpoint_name"]
                ],
                "s3_bucket_artifacts": param_values[param_names["s3_bucket_artifacts"]],
                "bedrock_region": param_values[param_names["bedrock_region"]],
                "access_key": param_values[param_names["access_key"]],
                "secret_key": param_values[param_names["secret_key"]],
            }
        if env != "local":
            log_frame_info(logger, f"Fetching SSM parameters for {env} environment.")
            param_values = self.get_ssm_parameters(list(param_names.values()))
            config = {
                "bedrock_model_region": param_values.get(
                    param_names["bedrock_model_region"], ""
                ),
                "bedrock_inference_config": param_values.get(
                    param_names["bedrock_inference_config"], ""
                ),
                "s3_bucket_artifacts": param_values.get(
                    param_names["s3_bucket_artifacts"], ""
                ),
                "s3_bucket_artifacts_config": param_values.get(
                    param_names["s3_bucket_artifacts_config"], ""
                ),
                "s3_bucket_artifacts_prompts": param_values.get(
                    param_names["s3_bucket_artifacts_prompts"], ""
                ),
                "s3_bucket_tracking": param_values.get(
                    param_names["s3_bucket_tracking"], ""
                ),
                "request_processing_queue_url": param_values.get(
                    param_names["request_processing_queue_url"], ""
                ),
                "notification_sucess_queue_url": param_values.get(
                    param_names["notification_sucess_queue_url"], ""
                ),
                "dynamodb_table_name": param_values.get(
                    param_names["dynamodb_table_name"], ""
                ),
                "dynamodb_record_ttl": param_values.get(
                    param_names["dynamodb_record_ttl"], ""
                ),
                "num_images_to_process": param_values.get(
                    param_names["num_images_to_process"], ""
                ),
                # Optional Gemini key
                "gemini_api_key": param_values.get(
                    param_names.get("gemini_api_key", ""), ""
                ),
                # Optional LLM provider selector (bedrock|gemini)
                "llm_provider": param_values.get(
                    param_names.get("llm_provider", ""), "bedrock"
                ),
            }
        log_frame_info(
            logger, message="Successfully fetched SSM config for environment: ", env=env
        )

        # Convert the configuration to JSON and store it in an environment variable
        json_str = json.dumps(config)
        os.environ["SSM_PARAM"] = json_str
        return config


# Base configuration class with default settings
class Config:
    ENV: str = "dev"  # Default environment is 'dev'
    APP_HOST: str = "0.0.0.0"  # Default host for the app
    APP_PORT: int = 8000  # Default port for the app


# Class to load the configuration and environment-specific settings
class LoadConfig(Config):
    log_frame_info(logger, "Initializing LoadConfig class.")

    # Get the environment variable for the environment (defaults to 'dev')
    env = os.getenv("ENV", "dev")
    log_frame_info(logger, message="Environment set to: ", env=env)

    # Create an instance of CustomConfig to load configurations
    custom_config = CustomConfig()
    log_frame_info(logger, "Fetching SSM configuration.")

    # Retrieve the SSM configuration for the specified environment
    config = custom_config.get_ssm_config(env)
    log_frame_info(logger, "SSM configuration retrieved successfully.")


# Function to get the configuration based on the environment
def get_config():
    env = os.getenv("ENV", "dev")
    log_frame_info(logger, message="Loading configuration for environment: ", env=env)

    # Return the configuration object for the current environment
    config_type = {env: LoadConfig()}

    log_frame_info(
        logger, message="Configuration loaded successfully for environment: ", env=env
    )
    return config_type[env]


# Retrieve the configuration object and store it in the 'config' variable
config: Config = get_config()
