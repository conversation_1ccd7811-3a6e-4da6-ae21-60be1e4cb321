import json
import logging
import boto3
import urllib3
import os

logger = logging.getLogger()
logger.setLevel(logging.INFO)

http = urllib3.PoolManager()

# Create boto3 clients for AWS services
sqs_client = boto3.client("sqs")
lambda_client = boto3.client("lambda")

# Get environment variables for queue and downstream lambda
REQUEST_PROCESSING_QUEUE_URL = os.getenv("REQUEST_PROCESSING_QUEUE_URL")
LISTING_QUALITY_API_LAMBDA = os.getenv("LISTING_QUALITY_API_LAMBDA")


# Function to fetch messages from the processing queue
def fetch_messages():
    try:
        response = sqs_client.receive_message(
            QueueUrl=REQUEST_PROCESSING_QUEUE_URL,
            MaxNumberOfMessages=10,  # Fetch maximum allowed batch size
            WaitTimeSeconds=1,       # Use short polling with 1 second timeout
        )
        return response.get("Messages", [])
    except Exception as e:
        logger.error(f"Error fetching messages from SQS: {e}")
        return []


# Function to process messages from the queue and invoke the API lambda for each valid message
def process_messages():
    while True:
        # Fetch messages from the processing queue
        messages = fetch_messages()
        if not messages:
            logger.info("No more messages in queue.")
            break

        for message in messages:
            try:
                # Extract and parse message body
                body = json.loads(message["Body"])
                api_type = body.get("type")
                submission_id = body.get("submission_id")
                
                # Attach the receipt handle to the body for later deletion
                body["message_handle"] = message["ReceiptHandle"]

                message_id = message["MessageId"]

                logger.info(
                    f"Request Id: {submission_id}, Message Id: {message_id}, api_type: {api_type}"
                )

                # Map API type to the appropriate endpoint
                if api_type == "IMAGE_VALIDATION":
                    api_endpoint = "/validate_quality"
                else:
                    api_endpoint = ""

                # Skip processing if endpoint mapping is not found
                if not api_endpoint:
                    logger.info(
                        f"Request Id: {submission_id}, Invalid or missing api_type: {api_type}"
                    )
                    continue
                
                # Create API Gateway compatible payload for the lambda
                payload = {
                    "resource": api_endpoint,
                    "path": api_endpoint,
                    "httpMethod": "POST",
                    "requestContext": {},
                    "multiValueQueryStringParameters": None,
                    "body": json.dumps(body),
                }

                # Invoke the API lambda asynchronously
                invoke_lambda_api(payload, submission_id)

            except json.JSONDecodeError:
                logger.error(f"Invalid JSON in message: {message['Body']}")
            except Exception as e:
                logger.error(f"Unexpected error processing message: {e}")


# Function to invoke the API lambda with the prepared payload
def invoke_lambda_api(payload, submission_id):
    try:
        # Convert payload to JSON string
        payload = json.dumps(payload)
        logger.info(
            f"Request Id: {submission_id}, Invoking Lambda: {LISTING_QUALITY_API_LAMBDA} with payload: {payload}"
        )

        # Invoke lambda asynchronously (Event invocation type)
        lambda_client.invoke(
            FunctionName=LISTING_QUALITY_API_LAMBDA,
            InvocationType="Event",  # Asynchronous invocation
            Payload=payload,
        )
    except Exception as e:
        logger.error(
            f"Request Id: {submission_id}, Error invoking listing quality Lambda: {e}"
        )


def lambda_handler(event, context):
    # Start processing messages from the queue
    process_messages()
    return {"status": "Processed all messages"}