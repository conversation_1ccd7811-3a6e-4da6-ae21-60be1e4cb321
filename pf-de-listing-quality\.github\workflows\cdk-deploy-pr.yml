name: Deploy-PR

on:
  issue_comment:
    types: [created] # will deploy only if a comment is made on a PR.

permissions:
  id-token: write # required to use OIDC authentication
  contents: write # required to checkout the code from the repo & for Branch Deployment
  pull-requests: write # required for Branch Deployment
  deployments: write # required for Branch Deployment
  checks: read # required for Branch Deployment
  statuses: read # required for Branch Deployment

jobs:
  deploy:
    name: Deploy PR
    environment: staging
    runs-on: codebuild-${{ github.event.repository.name }}-deploy-staging-${{ github.run_id }}-${{ github.run_attempt }}
    steps:
      - name: branch-deploy-step
        if: always()
        uses: github/branch-deploy@v9.3.0
        id: branch-deploy
        with:
          environment: staging
          stable_branch: master # changing this to master as it defaults to 'main' ---> keep this as 'master'
          checks: always # switch to 'required' temporarily for testing ---> Turn this back to 'always'
          disable_naked_commands: "true" # Set to true to avoid use of naked commands Eg: '.deploy' ---> keep this to 'true'
          param_separator: "|"
          skip_reviews: "staging"
          skip_ci: "staging"

      - id: checkout-code
        uses: actions/checkout@v4
        if: success() && steps.branch-deploy.outputs.continue == 'true'
        with:
          token: ${{ secrets.RW_GITHUB_TOKEN }}
          ref: ${{ steps.branch-deploy.outputs.ref }}

      - name: Deploy
        if: success()
        id: Deploy
        run: |
          export NVM_DIR="$HOME/.nvm"
          [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
          export DEPLOYMENT_ENV=staging
          cd ./infra/cdk/
          npm install
          npm run build
          cdk deploy --require-approval never --qualifier hnb659fds --context env=staging
          npm run deploy

      # Slack Notification
      - name: Set Slack message
        id: slack-message
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "::set-output name=message::*Workflow Status:* ${{ job.status }} :fast_parrot:"
          else
            echo "::set-output name=message::*Workflow Status:* ${{ job.status }} :alert2:"
          fi

      - name: Slack Notification
        id: slack
        if: always()
        uses: act10ns/slack@v2.1.0
        with:
          status: ${{ job.status }}
          steps: ${{ toJson(steps) }}
          # config: .github/slack.yml
          message: "${{ steps.slack-message.outputs.message }} \n
                    *Repository:* ${{ github.repository }} \n                                 
                    *Build URL:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}> \n
                    *Environment:* ${{ inputs.environment }} \n
                    *User:* ${{ github.actor }}\n
                    *Commit ID:* ${{ github.sha }} \n
                    *Branch:* ${{ github.ref }} \n
                    *Run ID:* ${{ github.run_number }}"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GHA_NOTIFICATION_SLACK_WEBHOOK_URL  }}
