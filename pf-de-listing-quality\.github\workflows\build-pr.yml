name: Build-PR
on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize

permissions:
    id-token: write # required to use OIDC authentication
    contents: read # required to checkout the code from the repo

env:
  IMAGE_REGISTRY: "995500545255.dkr.ecr.ap-southeast-1.amazonaws.com/pf-de-listing-quality"

jobs:
  Build-PR:
    name: Build-PR-Image
    runs-on: codebuild-${{ github.event.repository.name }}-build-staging-${{ github.run_id }}-${{ github.run_attempt }}
    steps:
      # Checkout Code
      - name: Chekout code
        id: checkout
        uses: actions/checkout@v4
      
      # Logging into AWS ECR
      - name: Login into AWS ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      # Creating Commit ID
      - name: Construct Commit ID
        id: create-commit-id
        run: |
          SHA_FULL=$(echo ${{ github.event.pull_request.head.sha }})
          SHA_10=$(echo ${{ github.event.pull_request.head.sha }} | head -c 10)
          echo "DOCKER_IMAGES_TAG=$SHA_10" >> $GITHUB_ENV
          echo "DOCKER_IMAGES_SHA_TAG=$SHA_FULL" >> $GITHUB_ENV
      
      # Debug filesystem structure
      - name: Debug directory structure
        id: debug
        run: |
          # Show current directory
          pwd
          # List current directory contents
          ls -la
          # Show all directories under the current one
          find . -type d -maxdepth 3

      # Build and push Docker image
      - name: Build & push Image
        id: build-push-image
        run: |
          cd ./app/lambdas/listing_quality/
          docker build -t ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_TAG .
          docker push ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_TAG

          docker tag ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_TAG ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_SHA_TAG
          docker push ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_SHA_TAG

          docker tag ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_TAG ${{ env.IMAGE_REGISTRY }}:pr-${{github.event.pull_request.number}}
          docker push ${{ env.IMAGE_REGISTRY }}:pr-${{github.event.pull_request.number}}
          
          docker tag ${{ env.IMAGE_REGISTRY }}:pr-$DOCKER_IMAGES_TAG ${{ env.IMAGE_REGISTRY }}:latest
          docker push ${{ env.IMAGE_REGISTRY }}:latest

      # Update Lambda function with latest image
      - name: Update Quality Listing Lambda function
        id: update-lambda
        run: |
          # Check if Lambda function exists
          if aws lambda get-function --function-name arn:aws:lambda:ap-southeast-1:995500545255:function:listing-quality-quality-listing-lambda-staging &>/dev/null; then
            echo "Lambda function found, updating with latest image..."
            aws lambda update-function-code \
              --function-name arn:aws:lambda:ap-southeast-1:995500545255:function:listing-quality-quality-listing-lambda-staging \
              --image-uri ${{ env.IMAGE_REGISTRY }}:latest
            echo "Lambda function updated successfully."
          else
            echo "Lambda function not found. This is likely the first run where the Lambda hasn't been created yet."
            echo "The Lambda function will be created by the CDK deployment."
          fi