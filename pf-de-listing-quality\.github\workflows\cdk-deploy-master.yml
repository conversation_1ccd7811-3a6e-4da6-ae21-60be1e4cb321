name: Deploy-Master

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        type: choice
        options:
          - staging
          - production
      docker_image_tag:
        description: 'The Tag of the Docker Image to deploy'
        required: true
        type: string  

permissions:
  id-token: write # required to use OIDC authentication
  contents: write # required to checkout the code from the repo & for Branch Deployment
  pull-requests: write # required for Branch Deployment
  deployments: write # required for Branch Deployment
  checks: read # required for Branch Deployment
  statuses: read # required for Branch Deployment

jobs:
  deploy:
    name: Deploy Master
    environment: ${{inputs.environment}}
    runs-on: codebuild-${{ github.event.repository.name }}-deploy-${{inputs.environment}}-${{ github.run_id }}-${{ github.run_attempt }}
    steps:
      - id: checkout-code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.RW_GITHUB_TOKEN }}
          ref: master

      - name: Set Docker Image Tag
        id: set-docker-tag
        run: |
          if [[ "${{ inputs.docker_image_tag }}" != master* ]]; then
            echo "DOCKER_IMAGE_TAG=master-${{ inputs.docker_image_tag }}" >> $GITHUB_ENV
          else
            echo "DOCKER_IMAGE_TAG=${{ inputs.docker_image_tag }}" >> $GITHUB_ENV
          fi
          echo "Using Docker image tag: $DOCKER_IMAGE_TAG"

      - name: Deploy
        if: success()
        id: Deploy
        run: |
          export NVM_DIR="$HOME/.nvm"
          [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
          nvm use 18.0.0
          
          # Set the deployment environment variable
          export DEPLOYMENT_ENV=${{ inputs.environment }}
          
          # Navigate to CDK directory
          cd ./infra/cdk/
          
          # Install dependencies and build
          npm install
          npm run build
          
          # Deploy with environment context and image tag context
          cdk deploy --require-approval never --context env=${{ inputs.environment }} --context imageTag=${{ env.DOCKER_IMAGE_TAG }} --qualifier hnb659fds
          npm run deploy

      # Slack Notification
      - name: Set Slack message
        id: slack-message
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "::set-output name=message::*Workflow Status:* ${{ job.status }} :fast_parrot:"
          else
            echo "::set-output name=message::*Workflow Status:* ${{ job.status }} :alert2:"
          fi

      - name: Slack Notification
        id: slack
        if: always()
        uses: act10ns/slack@v2.1.0
        with:
          status: ${{ job.status }}
          steps: ${{ toJson(steps) }}
          # config: .github/slack.yml
          message: "${{ steps.slack-message.outputs.message }} \n
                    *Repository:* ${{ github.repository }} \n                                 
                    *Build URL:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}> \n
                    *Environment:* ${{ inputs.environment }} \n         
                    *User:* ${{ github.actor }}\n
                    *Commit ID:* ${{ github.sha }} \n
                    *Branch:* ${{ github.ref }} \n
                    *Docker Image Tag:* ${{ env.DOCKER_IMAGE_TAG }} \n
                    *Run ID:* ${{ github.run_number }}"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GHA_NOTIFICATION_SLACK_WEBHOOK_URL  }}