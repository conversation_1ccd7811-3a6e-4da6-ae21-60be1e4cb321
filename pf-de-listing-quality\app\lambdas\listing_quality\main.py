import json
import os
import sys  # Ensure logs flush in AWS Lambda
import uuid
from contextlib import asynccontextmanager

import uvicorn
from dotenv import find_dotenv, load_dotenv
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from mangum import Mangum

from api import router
from core.logger.logger import (
    get_logger,
    trace_id_var,
    log_frame_info,
    log_frame_debug,
    log_frame_warning,
)
from core.services.config_loader import PreConfigParameters
from core.services.prompt_loads import PromptLoader

# Initialize logger
logger = get_logger(__name__)

# Load environment variables from .env file
load_dotenv(find_dotenv())
log_frame_info(logger, "Environment Complete")

# Print all loaded environment variables in DEBUG mode
for key, value in os.environ.items():
    if value:
        log_frame_debug(logger, message="Loaded ENV", key=key, value=value)
    else:
        log_frame_warning(logger, message="Missing ENV", key=key)

# OpenAPI documentation tags
tags_metadata = [
    {
        "name": "Validate Quality of Image",
        "description": "API to validate the quality of the image from the image URL given",
    },
]

# Factory function to create the FastAPI app
def create_app() -> FastAPI:
    @asynccontextmanager
    async def lifespan(app: FastAPI):
        """Startup tasks: load config and prompts when the app starts."""

        # Load configuration file from S3
        log_frame_info(logger, "Loading config at startup...")
        PreConfigParameters.load_config_once()

        # Load prompt templates from S3
        log_frame_info(logger, "Loading prompts at startup...")
        await PromptLoader.load_prompts()
        log_frame_info(logger, "All prompts loaded successfully.")
        yield

    # Create FastAPI app with custom OpenAPI tags and lifecycle handler
    app = FastAPI(openapi_tags=tags_metadata, lifespan=lifespan)

    @app.middleware("http")
    async def add_trace_id(request: Request, call_next):
        """Middleware to assign or extract a trace ID for logging."""

        # Try to get trace_id from request header
        trace_id = request.headers.get("X-Request-ID")

        # If not in header, try to extract from request body
        if not trace_id:
            try:
                body = await request.body()
                if body:
                    data = json.loads(body.decode("utf-8"))
                    # Use submission_id as trace_id if available
                    trace_id = data.get("submission_id") or str(uuid.uuid4())
            except json.JSONDecodeError:
                pass  # Ignore if body is not valid JSON

        # If trace_id is still not set, generate a new one
        if not trace_id:
            trace_id = str(uuid.uuid4())

        # Store trace_id for logging
        trace_id_var.set(trace_id)
        log_frame_info(
            logger, message="New request received - trace_id", trace_id=trace_id
        )

        # Call the next middleware/route handler
        response = await call_next(request)

        # Flush stdout to ensure logs appear in AWS Lambda CloudWatch
        sys.stdout.flush()

        return response

    # Add CORS middleware to allow all origins (customize in production)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Set specific domains in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Register application routes
    app.include_router(router)

    return app

# Create the app and wrap it for AWS Lambda deployment
app = create_app()
handler = Mangum(app)

# Run locally for development/debugging
if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
