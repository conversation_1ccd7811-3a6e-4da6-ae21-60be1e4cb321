import yaml
import boto3
from core.logger.logger import (
    get_logger,
    log_frame_error,
    log_frame_info,
    log_frame_warning,
)
from config.config import config

# Initialize S3 client to interact with AWS S3
s3_client = boto3.client("s3")
config = config.config

# Global variable to store the loaded config
LOADED_CONFIG = None  # Will store YAML content once it is loaded

# Initialize logger for logging information and errors
logger = get_logger(__name__)


# Class to handle loading configuration and fetching quality validation secrets
class PreConfigParameters:
    _loaded_config = None  # Class-level variable to store the loaded configuration

    # Class method to load the configuration only once
    @classmethod
    def load_config_once(cls):
        # If configuration has already been loaded, return without loading again
        if cls._loaded_config is not None:
            return

        try:
            # Retrieve S3 bucket and directory from config
            bucket_name = config.get("s3_bucket_artifacts")
            bucket_dir = config.get("s3_bucket_artifacts_config")

            # Define the S3 key for the YAML config file
            yaml_file_key = f"{bucket_dir}/config.yaml"

            # Fetch the YAML file from the S3 bucket
            response = s3_client.get_object(Bucket=bucket_name, Key=yaml_file_key)

            # Read the content of the YAML file and parse it
            yaml_content = response["Body"].read()
            cls._loaded_config = yaml.safe_load(yaml_content)  # Load YAML content

        except Exception as e:
            # Log an error if fetching or parsing the YAML file fails
            log_frame_error(
                logger, message="Error fetching config.yaml from S3", error=str(e)
            )
            cls._loaded_config = None

    # Class method to retrieve quality validation secrets from the loaded config
    @classmethod
    def get_quality_validation_secrets(cls):
        # If configuration is not yet loaded, attempt to load it
        if cls._loaded_config is None:
            log_frame_error(
                logger, message="Config not loaded yet, attempting to load..."
            )
            cls.load_config_once()  # Try loading the configuration

        # If config still not loaded, log an error and return None
        if cls._loaded_config is None:
            log_frame_error(logger, message="Configuration still not loaded. Aborting.")
            return None

        # Retrieve the 'QUALITY_VALIDATION_CONFIG_PARAMETERS' section from the loaded config
        pre_configs = cls._loaded_config.get("QUALITY_VALIDATION_CONFIG_PARAMETERS")

        # If the section is not found, log a warning and return None
        if pre_configs is None:
            log_frame_warning(
                logger,
                message="QUALITY_VALIDATION_CONFIG_PARAMETERS not found in the YAML file.",
            )
            return None

        # Log success when the section is found and return the configuration
        log_frame_info(
            logger,
            message="Successfully retrieved QUALITY_VALIDATION_CONFIG_PARAMETERS.",
        )
        return pre_configs
