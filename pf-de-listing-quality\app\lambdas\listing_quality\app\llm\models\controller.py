import os
from http import HTTPStatus
from config.config import config
from core.exceptions.custom import CustomException

from core.logger.logger import get_logger, log_frame_info, log_frame_debug

from .bedrock import BedrockLlm
from .gemini import GeminiLlm

# Load configuration object from the config module
config = config.config

# Get the current environment (e.g., development, staging, production)
env = os.getenv("ENV")

# Determine whether to use the `invoke_model` method instead of `converse_model`
# Fallback default is "false" (i.e., use `converse_model` by default)
use_bedrock_invoke = os.getenv("USE_BEDROCK_INVOKE", "false")

# Select which provider to use (bedrock|gemini) from SSM-backed config with env fallback
llm_provider = str(config.get("llm_provider") or os.getenv("LLM_PROVIDER", "bedrock")).lower()

# Initialize a logger specific to this module
logger = get_logger(__name__)


class ModelController:
    def get_model_instance(self, image, prompt="", business_rules=None, is_verification=False):
        # Log the start of model instance fetching process
        log_frame_info(logger, message="Fetching model instance")
        log_frame_debug(logger, message=f"Parameters: business_rules={business_rules is not None}, is_verification={is_verification}")

        # Route to Gemini if selected
        if llm_provider == "gemini":
            log_frame_debug(logger, message="Routing to Gemini model...")

            # Use converse_model for structured data extraction if business_rules or verification is needed
            if business_rules or is_verification:
                # Convert image and prompt to message format for converse_model
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"image": {"source": {"bytes": image}}},
                            {"text": prompt}
                        ]
                    }
                ]
                return GeminiLlm.converse_model(messages, business_rules, is_verification)
            else:
                # Use converse_image_text for simple image+text processing
                return GeminiLlm.converse_image_text(image, prompt)

        # Otherwise route to Bedrock
        if use_bedrock_invoke.lower() == "true":
            log_frame_debug(logger, message="Invoking Bedrock model...")
            result = BedrockLlm.invoke_model(image, prompt)
            # Normalize return format to match Gemini
            if isinstance(result, str):
                return {
                    "status": "SUCCESS",
                    "result": result,
                    "llm_usage": {}
                }
            return result

        log_frame_debug(logger, message="Conversing Bedrock model...")
        return BedrockLlm.converse_model(image, prompt)
