import pytest
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from botocore.exceptions import Client<PERSON>rror
from app.llm.models.bedrock import BedrockLlm
from core.enums.status import ResponseStatus

# Sample input
sample_image_base64 = "fake_base64_image_data"
sample_prompt = "Describe the content of the image."


@pytest.fixture
def mock_bedrock_client():
    with patch("app.llm.models.bedrock.bedrock_runtime") as mock_client:
        yield mock_client

@pytest.fixture
def mock_logger():
    with patch("app.llm.models.bedrock.logger") as mock_logger:
        yield mock_logger

def test_invoke_model_success(mock_bedrock_client, mock_logger):
    # Setup mocked response
    fake_response = {
        "body": MagicMock(read=MagicMock(return_value=b'{"content": [{"text": "This is a test."}]}'))
    }
    mock_bedrock_client.invoke_model.return_value = fake_response

    result = BedrockLlm.invoke_model(sample_image_base64, sample_prompt)
    assert result == "This is a test."

def test_invoke_model_throttle(mock_bedrock_client):
    # Define a mock ThrottlingException
    class ThrottlingException(Exception):
        pass

    mock_bedrock_client.exceptions.ThrottlingException = ThrottlingException
    mock_bedrock_client.invoke_model.side_effect = ThrottlingException("Rate limit")

    result = BedrockLlm.invoke_model(sample_image_base64, sample_prompt)
    assert result["status"] == ResponseStatus.THROTTLE.value


def test_converse_model_success(mock_bedrock_client):
    # Setup mocked response
    mock_bedrock_client.converse.return_value = {
        "output": {
            "message": {
                "content": [{"text": "{'output': 'some value'}"}]
            }
        },
        "usage": {"tokens": 10},
        "metrics": {"latency_ms": 50}
    }

    result = BedrockLlm.converse_model(b"fake_image_bytes", sample_prompt)
    assert "result" in result
    assert "llm_usage" in result
    assert isinstance(result["result"], dict)

def test_converse_model_throttle(mock_bedrock_client):
    class ThrottlingException(Exception):
        pass

    mock_bedrock_client.exceptions.ThrottlingException = ThrottlingException
    mock_bedrock_client.converse.side_effect = ThrottlingException("Throttled")

    result = BedrockLlm.converse_model(b"fake_image_bytes", sample_prompt)
    assert result["status"] == ResponseStatus.THROTTLE.value


@pytest.mark.parametrize("response_str,expected", [
    ("{'key': 'value'}", {"key": "value"}),
    ("<output>This is XML</output>", "<output>This is XML</output>"),
    ("invalid dict string", "invalid dict string")
])
def test_parse_response(response_str, expected):
    result = BedrockLlm.parse_response(response_str)
    assert result == expected
