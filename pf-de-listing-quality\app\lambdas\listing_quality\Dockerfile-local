# Use the official Python image from the Docker Hub
FROM python:3.11
 
# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
 
# Set the working directory
WORKDIR /app
 
# Copy the requirements file
COPY requirements.txt .
 
# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
 
# Copy the FastAPI application code
COPY . /app
 
ENV ENV=dev
ENV LOG_LEVEL=INFO
ENV MOCK_LLM_RESPONSE=False
ENV LLM_MODEL=bedrock-2023-05-31
ENV AWS_DEFAULT_REGION=ap-southeast-1
ENV USE_BEDROCK_INVOKE=False
ENV AWS_ACCESS_KEY_ID=
ENV AWS_SECRET_ACCESS_KEY=
ENV AWS_SESSION_TOKEN=
ENV AWS_ACCESS_KEY_ID_TEST=
ENV AWS_SECRET_ACCESS_KEY_TEST=
ENV AWS_SESSION_TOKEN_TEST=
# Command to run the FastAPI app with <PERSON>vicorn
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
# CMD ["python3", "main.py"]