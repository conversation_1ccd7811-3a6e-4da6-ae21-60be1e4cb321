import os
import json
import pytest
import boto3
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone, timedelta
from moto import mock_aws

os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
os.environ["ENV"] = "staging"

ENV = os.environ["ENV"]
TEST_S3_BUCKET = "listing-quality-requests-tracking-staging-nbs"
TEST_DYNAMODB_TABLE = "listing-quality-requests-tracking-staging-nbs"
TEST_DYNAMODB_TTL = "48"
TEST_SUBMISSION_ID = "test-submission-123"

# Import the lambda function
import sys
sys.path.append('.')  # Adjust if needed to find your lambda code

# Mock SSM Parameter Store
@pytest.fixture
def ssm():
    with mock_aws():
        ssm_client = boto3.client('ssm', region_name='us-east-1')
        
        # Create SSM parameters
        ssm_client.put_parameter(
            Name=f"/listing-quality/{ENV}/s3/tracking",
            Value=TEST_S3_BUCKET,
            Type="String"
        )
        ssm_client.put_parameter(
            Name=f"/listing-quality/{ENV}/dynamodb/table",
            Value=TEST_DYNAMODB_TABLE,
            Type="String"
        )
        ssm_client.put_parameter(
            Name=f"/listing-quality/{ENV}/dynamodb/ttl",
            Value=TEST_DYNAMODB_TTL,
            Type="String"
        )
        
        yield ssm_client

mock_ssm_client = MagicMock()
with patch('boto3.client', return_value=mock_ssm_client):
    from report_failed_requests import lambda_handler, log_failure_to_dynamodb, write_json_to_s3

@pytest.fixture
def mock_ssm():
    """Create a mock SSM client."""
    # Reset the mock before each test
    mock_ssm_client.reset_mock()
    return mock_ssm_client

# Set up environment variables for testing
@pytest.fixture(autouse=True)
def mock_env_variables():
    with patch.dict(os.environ, {
        "ENV": ENV,
    }):
        yield

# Mock S3 service
@pytest.fixture
def s3():
    with mock_aws():
        s3_client = boto3.client('s3', region_name='us-east-1')
        s3_client.create_bucket(Bucket=TEST_S3_BUCKET)
        yield s3_client

# Mock DynamoDB service
@pytest.fixture
def dynamodb():
    with mock_aws():
        dynamodb_resource = boto3.resource('dynamodb', region_name='us-east-1')
        
        # Create test table
        table = dynamodb_resource.create_table(
            TableName=TEST_DYNAMODB_TABLE,
            KeySchema=[{'AttributeName': 'RequestID', 'KeyType': 'HASH'}],
            AttributeDefinitions=[{'AttributeName': 'RequestID', 'AttributeType': 'S'}],
            ProvisionedThroughput={'ReadCapacityUnits': 5, 'WriteCapacityUnits': 5}
        )
        
        yield dynamodb_resource

# Sample SQS event payload
@pytest.fixture
def sqs_event():
    return {
        "Records": [
            {
                "messageId": "test-message-id-1",
                "body": json.dumps({
                    "submission_id": TEST_SUBMISSION_ID,
                    "type": "listing_quality",
                    "data": {"example": "data"}
                })
            },
            {
                "messageId": "test-message-id-2",
                "body": json.dumps({
                    "submission_id": "test-submission-456",
                    "type": "listing_quality",
                    "data": {"example": "more data"}
                })
            }
        ]
    }

# Sample event with error case
@pytest.fixture
def sqs_event_with_error():
    return {
        "Records": [
            {
                "messageId": "test-message-id-1",
                "body": json.dumps({
                    "submission_id": TEST_SUBMISSION_ID,
                    "type": "listing_quality",
                    "data": {"example": "data"}
                })
            },
            {
                "messageId": "test-message-id-error",
                "body": "invalid-json-data"  # This will cause a JSON parsing error
            }
        ]
    }

# Test writing to S3
@patch('report_failed_requests.fetch_ssm_parameters')
def test_write_json_to_s3(mock_ssm_params, s3):
    # Setup mock SSM parameters
    mock_ssm_params.return_value = {
        f"/listing-quality/{ENV}/s3/tracking": TEST_S3_BUCKET,
        f"/listing-quality/{ENV}/dynamodb/table": TEST_DYNAMODB_TABLE,
        f"/listing-quality/{ENV}/dynamodb/ttl": TEST_DYNAMODB_TTL
    }
    
    # Test data
    submission_id = TEST_SUBMISSION_ID
    test_data = {"test": "data", "type": "test_type"}
    
    # Call the function
    write_json_to_s3(submission_id, test_data)

# Test S3 write error handling
@patch('report_failed_requests.fetch_ssm_parameters')
@patch('report_failed_requests.s3_client.put_object')
def test_write_json_to_s3_error(mock_s3_put, mock_ssm_params):
    # Setup mock SSM parameters
    mock_ssm_params.return_value = {
        f"/listing-quality/{ENV}/s3/tracking": TEST_S3_BUCKET,
        f"/listing-quality/{ENV}/dynamodb/table": TEST_DYNAMODB_TABLE,
        f"/listing-quality/{ENV}/dynamodb/ttl": TEST_DYNAMODB_TTL
    }
    
    # Setup S3 error
    mock_s3_put.side_effect = Exception("Test S3 error")
    
    # Test data
    submission_id = TEST_SUBMISSION_ID
    test_data = {"test": "data"}
    
    # Call the function and expect an exception
    with pytest.raises(Exception) as excinfo:
        write_json_to_s3(submission_id, test_data)
    
    # Verify the exception message contains useful info
    assert "Error uploading file to S3" in str(excinfo.value)
    assert submission_id in str(excinfo.value)

# Test logging to DynamoDB - insert new record
@patch('report_failed_requests.fetch_ssm_parameters')
def test_log_failure_to_dynamodb_new_record(mock_ssm_params, dynamodb):
    # Setup mock SSM parameters
    mock_ssm_params.return_value = {
        f"/listing-quality/{ENV}/s3/tracking": TEST_S3_BUCKET,
        f"/listing-quality/{ENV}/dynamodb/table": TEST_DYNAMODB_TABLE,
        f"/listing-quality/{ENV}/dynamodb/ttl": TEST_DYNAMODB_TTL
    }
    
    # Setup for test - ensure the table exists
    table = dynamodb.Table(TEST_DYNAMODB_TABLE)
    
    # Import lambda function with patched module-level variables
    from report_failed_requests import DYNAMODB_TABLE
    
    # Override the table reference
    with patch('report_failed_requests.DYNAMODB_TABLE', table):
        # Test data
        submission_id = TEST_SUBMISSION_ID
        body = {"type": "listing_quality", "data": {"test": "value"}}
        
        # Call the function
        log_failure_to_dynamodb(submission_id, body)
        
        # Verify the item was added to DynamoDB
        response = table.get_item(Key={"RequestID": submission_id})
        assert "Item" in response
        assert response["Item"]["RequestID"] == submission_id
        assert response["Item"]["status"] == "FAILED"
        assert response["Item"]["api_type"] == "listing_quality"
        assert "payload" in response["Item"]
        assert "timestamp" in response["Item"]
        assert "ttl" in response["Item"]

# Test logging to DynamoDB - update existing record
@patch('report_failed_requests.fetch_ssm_parameters')
def test_log_failure_to_dynamodb_update_record(mock_ssm_params, dynamodb):
    # Setup mock SSM parameters
    mock_ssm_params.return_value = {
        f"/listing-quality/{ENV}/s3/tracking": TEST_S3_BUCKET,
        f"/listing-quality/{ENV}/dynamodb/table": TEST_DYNAMODB_TABLE,
        f"/listing-quality/{ENV}/dynamodb/ttl": TEST_DYNAMODB_TTL
    }
    
    # Setup for test - ensure the table exists and has an item
    table = dynamodb.Table(TEST_DYNAMODB_TABLE)
    
    # Create an initial item
    initial_item = {
        "RequestID": TEST_SUBMISSION_ID,
        "status": "PROCESSING",
        "api_type": "listing_quality",
        "payload": json.dumps({"original": "data"}),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    table.put_item(Item=initial_item)
    
    # Import lambda function with patched module-level variables
    from report_failed_requests import DYNAMODB_TABLE
    
    # Override the table reference
    with patch('report_failed_requests.DYNAMODB_TABLE', table):
        # Test data
        submission_id = TEST_SUBMISSION_ID
        body = {"type": "listing_quality", "data": {"test": "updated_value"}}
        
        # Call the function
        log_failure_to_dynamodb(submission_id, body)
        
        # Verify the item was updated in DynamoDB
        response = table.get_item(Key={"RequestID": submission_id})
        assert "Item" in response
        assert response["Item"]["RequestID"] == submission_id
        assert response["Item"]["status"] == "FAILED"  # Status should be updated
        assert response["Item"]["api_type"] == "listing_quality"  # Should remain unchanged

# Main Lambda handler test - success case
@patch('report_failed_requests.fetch_ssm_parameters')
@patch('report_failed_requests.write_json_to_s3')
@patch('report_failed_requests.log_failure_to_dynamodb')
def test_lambda_handler_success(mock_log_dynamodb, mock_write_s3, mock_ssm_params, sqs_event):
    # Setup mock SSM parameters
    mock_ssm_params.return_value = {
        f"/listing-quality/{ENV}/s3/tracking": TEST_S3_BUCKET,
        f"/listing-quality/{ENV}/dynamodb/table": TEST_DYNAMODB_TABLE,
        f"/listing-quality/{ENV}/dynamodb/ttl": TEST_DYNAMODB_TTL
    }
    
    # Call the lambda handler
    response = lambda_handler(sqs_event, {})
    
    # Verify response
    assert response["statusCode"] == 200
    assert "Processed all failed requests successfully" in response["body"]
    
    # Verify each record was processed
    assert mock_write_s3.call_count == 2
    assert mock_log_dynamodb.call_count == 2

# Test partial failure in records processing
@patch('report_failed_requests.fetch_ssm_parameters')
@patch('report_failed_requests.write_json_to_s3')
@patch('report_failed_requests.log_failure_to_dynamodb')
def test_lambda_handler_partial_failure(mock_log_dynamodb, mock_write_s3, mock_ssm_params, sqs_event_with_error):
    # Setup mock SSM parameters
    mock_ssm_params.return_value = {
        f"/listing-quality/{ENV}/s3/tracking": TEST_S3_BUCKET,
        f"/listing-quality/{ENV}/dynamodb/table": TEST_DYNAMODB_TABLE,
        f"/listing-quality/{ENV}/dynamodb/ttl": TEST_DYNAMODB_TTL
    }
    
    # Call the lambda handler
    response = lambda_handler(sqs_event_with_error, {})
    
    # Verify response - should still be success overall
    assert response["statusCode"] == 200
    assert "Processed all failed requests successfully" in response["body"]
    
    # Verify only valid records were fully processed
    assert mock_write_s3.call_count == 1
    assert mock_log_dynamodb.call_count == 1