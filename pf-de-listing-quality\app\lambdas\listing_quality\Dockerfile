# Use the AWS Lambda provided Python base image
FROM public.ecr.aws/lambda/python:3.11
 
# Copy the application code and requirements
COPY . ${LAMBDA_TASK_ROOT}
 
# Install the necessary dependencies
RUN pip install --no-cache-dir -r ${LAMBDA_TASK_ROOT}/requirements.txt

ENV ENV=dev
ENV LOG_LEVEL=INFO
ENV MOCK_LLM_RESPONSE=False
ENV LLM_MODEL=bedrock-2023-05-31
ENV AWS_DEFAULT_REGION=ap-southeast-1
ENV AWS_ACCESS_KEY_ID_TEST=
ENV AWS_SECRET_ACCESS_KEY_TEST=
ENV AWS_SESSION_TOKEN_TEST=

# Command to run the application
CMD ["main.handler"]
