# tests/test_config_loader.py

from unittest.mock import patch

from core.services.config_loader import PreConfigParameters



def test_get_quality_validation_secrets_already_loaded():
    # Simulate config already loaded
    PreConfigParameters._loaded_config = {
        "QUALITY_VALIDATION_CONFIG_PARAMETERS": {
            "pre_config_competitors_list": ["test"]
        }
    }

    result = PreConfigParameters.get_quality_validation_secrets()
    assert result["pre_config_competitors_list"] == ["test"]


def test_get_quality_validation_secrets_missing_key():
    # Config exists but missing the target key
    PreConfigParameters._loaded_config = {"something_else": 123}

    result = PreConfigParameters.get_quality_validation_secrets()
    assert result is None


def test_get_quality_validation_secrets_returns_none_on_s3_failure(monkeypatch):
    # Simulate S3 failure
    PreConfigParameters._loaded_config = None

    def fake_get_object(*args, **kwargs):
        raise Exception("Simulated S3 error")

    with patch("core.services.config_loader.s3_client.get_object", fake_get_object):
        result = PreConfigParameters.get_quality_validation_secrets()
        assert result is None


def test_load_config_once_skips_if_loaded():
    PreConfigParameters._loaded_config = {"already": "set"}

    # Should not attempt to re-fetch
    with patch("core.services.config_loader.s3_client.get_object") as mock_get:
        PreConfigParameters.load_config_once()
        mock_get.assert_not_called()
