import json
import boto3
import os
import hashlib
import logging
from datetime import datetime, timezone, timedelta

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Create boto3 clients for AWS services
sqs_client = boto3.client("sqs")
dynamodb_client = boto3.resource("dynamodb")
s3_client = boto3.client("s3")
ssm_client = boto3.client("ssm")

# Get environment variables
ENV = os.getenv("ENV")

# SSM parameter key names for configuration
S3_DIRECTORY = "failed-validations-requests"
SSM_S3_BUCKET_KEY = f"/listing-quality/{ENV}/s3/tracking"
SSM_DYNAMODB_TABLE_KEY = f"/listing-quality/{ENV}/dynamodb/table"
SSM_DYNAMODB_TTL_KEY = f"/listing-quality/{ENV}/dynamodb/ttl"
SSM_NOTIFICATION_QUEUE_KEY = f"/listing-quality/{ENV}/queue/notification"

# Function to fetch SSM parameters from AWS Parameter Store
def fetch_ssm_parameters():
    parameter_names = [
        SSM_S3_BUCKET_KEY,
        SSM_DYNAMODB_TABLE_KEY,
        SSM_DYNAMODB_TTL_KEY,
        SSM_NOTIFICATION_QUEUE_KEY,
    ]

    try:
        response = ssm_client.get_parameters(
            Names=parameter_names, WithDecryption=True
        )
        parameters = {}
        for param in response["Parameters"]:
            parameters[param["Name"]] = param["Value"]

        return parameters
    except Exception as e:
        logger.error(f"Error fetching SSM parameters: {str(e)}", exc_info=True)
        raise


# Fetch configuration from SSM Parameter Store
ssm_params = fetch_ssm_parameters()

# Initialize configuration variables from SSM parameters
NOTIFICATION_SUCESS_QUEUE_URL = ssm_params.get(SSM_NOTIFICATION_QUEUE_KEY)
S3_BUCKET_NAME = ssm_params.get(SSM_S3_BUCKET_KEY)
DYNAMODB_TABLE_NAME = ssm_params.get(SSM_DYNAMODB_TABLE_KEY)
DYNAMODB_RECORD_TTL = int(ssm_params.get(SSM_DYNAMODB_TTL_KEY)) if ssm_params.get(SSM_DYNAMODB_TTL_KEY) else 2

# Initialize DynamoDB table resource
DYNAMODB_TABLE = dynamodb_client.Table(DYNAMODB_TABLE_NAME) if DYNAMODB_TABLE_NAME else None

# URLs of priority queues from environment variables, organized by customer tier
QUEUE_URLS = {
    "diamond": os.getenv("DIAMOND_QUEUE_URL"),
    "ruby": os.getenv("RUBY_QUEUE_URL"),
    "sapphire": os.getenv("SAPPHIRE_QUEUE_URL"),
    "emerald": os.getenv("EMERALD_QUEUE_URL"),
}

# Function to log validation failures in DynamoDB for tracking and analysis
def log_failure_to_dynamodb(submission_id, body, error_message):
    try:
        data = {
            "RequestID": submission_id,
            "api_type": body.get("type"),
            "output": json.dumps({"error_message": error_message}),
            "payload": json.dumps(body),
            "status": "FAILED_VALIDATION",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "ttl": int(
                (
                    datetime.now(timezone.utc) + timedelta(hours=DYNAMODB_RECORD_TTL)
                ).timestamp()
            ),
        }
        DYNAMODB_TABLE.put_item(Item=data)
        logger.info("Inserted failed Request Id: %s", submission_id)
    except Exception as e:
        logger.error(
            f"Failed to log Request Id: {submission_id} to DynamoDB: {str(e)}",
            exc_info=True,
        )
        raise Exception(
            f"Failed to log Request Id: {submission_id} to DynamoDB: {str(e)}"
        )


# Function to store failed validation payloads in S3 for later analysis
def write_json_to_s3(submission_id, body, error_message):
    body["error_message"] = error_message
    json_str = json.dumps(body)
    s3_path = f"{S3_DIRECTORY}/{submission_id}.json"
    try:
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=s3_path,
            Body=json_str,
            ContentType="application/json",
        )
        logger.info(
            f"Successfully uploaded Request Id: {submission_id}.json to S3://{S3_BUCKET_NAME}/{s3_path}"
        )
    except Exception as e:
        logger.error(
            f"Error uploading file to S3: {e}, Request Id: {submission_id}, data: {json_str}"
        )
        raise Exception(
            f"Error uploading file to S3: {e}, Request Id: {submission_id}, data: {json_str}"
        )

# Function to notify downstream systems about validation failures
def send_message_to_notification_queue(body, error_message):
    if os.getenv("DISABLE_NOTIFICATIONS", "false").lower() == "true":
        logger.info("Notifications are disabled via DISABLE_NOTIFICATIONS env var. Skipping notification.")
        return
        
    body["error_message"] = error_message
    try:
        sqs_client.send_message(
            QueueUrl=NOTIFICATION_SUCESS_QUEUE_URL, MessageBody=json.dumps(body)
        )
        logger.info("Successfully added message in notification queue.")
    except Exception as e:
        logger.error("Failed to add message in notification queue.")
        logger.exception(e)

# Function to handle validation errors by updating DynamoDB, S3, and sending notifications
def log_validation_error(body, error_message):
    submission_id = body.get("submission_id")
    if submission_id:
        log_failure_to_dynamodb(submission_id, body, error_message)
        write_json_to_s3(submission_id, body, error_message)
    send_message_to_notification_queue(body, error_message)

# Function to perform type-specific validations based on request type
def validate_payload_by_type_field(body, submission_id):
    request_type = body.get("type", "")
    if request_type == "IMAGE_VALIDATION":
        images = body.get("images")
        if not images:
            error_message = f"Request Id: {submission_id}, Invalid or missing images for {request_type}."
            logger.error(error_message)
            log_validation_error(body, error_message)
            return False
    return True

def lambda_handler(event, context):
    try:
        for record in event["Records"]:
            message_id = record["messageId"]
            body = record["body"]
            logger.info(f"Processing message_id: {message_id}, body: {body}")

            # Parse the message body as JSON
            try:
                body = json.loads(body)
            except json.JSONDecodeError:
                body = {"payload": body}
                error_message = "Invalid JSON format"
                logger.error(error_message)
                log_validation_error(body, error_message)
                continue

            # Validate submission ID presence
            submission_id = body.get("submission_id")
            if not submission_id:
                error_message = f"Missing submission_id: {submission_id}"
                logger.error(error_message)
                log_validation_error(body, error_message)
                continue

            # Validate customer type and ensure it maps to a defined queue
            customer_type = body.get("customer_type", "").lower()
            if customer_type not in QUEUE_URLS:
                error_message = f"Request Id: {submission_id}, Invalid customer type: {customer_type}"
                logger.error(error_message)
                log_validation_error(body, error_message)
                continue

            # Validate API type/endpoint presence
            path = body.get("type")
            if not path:
                error_message = f"Request Id: {submission_id}, Invalid or missing endpoint: {path}"
                logger.error(error_message)
                log_validation_error(body, error_message)
                continue

            # Validate listing ID format and value
            listing_id = body.get("listing_id")
            if not isinstance(listing_id, int) or listing_id <= 0:
                error_message = f"Request Id: {submission_id}, Invalid or missing listing_id: {listing_id}"
                logger.error(error_message)
                log_validation_error(body, error_message)
                continue
            
            # Validate country code presence
            country_code = body.get("country_code")
            if not country_code:
                error_message = f"Request Id: {submission_id}, Invalid or missing country_code: {country_code}"
                logger.error(error_message)
                log_validation_error(body, error_message)
                continue
            
            # Perform API-type specific validations
            if not validate_payload_by_type_field(body, submission_id):
                continue

            # Prepare message parameters for SQS based on queue type (standard or FIFO)
            queue_url = QUEUE_URLS[customer_type]
            params = {"QueueUrl": queue_url, "MessageBody": json.dumps(body)}
            if queue_url.endswith(".fifo"):
                params["MessageGroupId"] = customer_type
                params["MessageDeduplicationId"] = hashlib.sha256(
                    json.dumps(body).encode()
                ).hexdigest()
            logger.info(f"Request Id: {submission_id}, SQS parameters: {params}")

            # Send the validated message to the appropriate customer tier queue
            sqs_client.send_message(**params)
            logger.info(f"Message sent successfully, Request Id: {submission_id}")

        return {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": f"Messages sent successfully."
                }
            ),
        }
    except Exception as e:
        logger.error(f"Unhandled error in lambda function: {e}")
        return {
            "statusCode": 500,
            "body": json.dumps({"message": "Internal Server Error", "error": str(e)}),
        }